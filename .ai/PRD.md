# Multifamily Deal Calculator PRD

### TL;DR

A simple web-based calculator for multifamily real estate investors.
Users input a property's purchase price and expected monthly rent; the
app instantly applies the 1% rule, displaying a clear 'Deal' or 'No
Deal' recommendation. Designed for maximum clarity, speed, and
approachability, this tool targets new and aspiring investors who want
fast guidance with minimal effort.

---

## Goals

### Business Goals

- Increase brand awareness among beginner multifamily investors

- Drive qualified user traffic to our platform

- Funnel users towards account creation or further product engagement

- Validate market interest in simple investing tools

### User Goals

- Get immediate feedback on a potential deal without spreadsheets or
  research

- Access the tool easily from any device (mobile first)

- Trust results thanks to simple, transparent logic

- Rapidly filter out properties that don’t meet baseline investment
  criteria

### Non-Goals

- No complex underwriting, expense breakdowns, or advanced analytics

- No PDF or downloadable reports

- No account creation, pro features, or gated content for v1

---

## User Stories

**Persona:** New Real Estate Investor (Nancy)

- As a new investor, I want to see if a property passes the 1% rule, so
  that I immediately know if it’s worth more research.

- As a returning user, I want to quickly analyze several properties in a
  row, so that I can shortlist deals efficiently.

- As a mobile user, I want the experience to be as simple and accessible
  as checking a stock price, so that I can use the calculator on the go.

---

## Functional Requirements

- **Deal Calculation (Priority: Highest)**

  - 1% Rule Checker: Accepts purchase price and monthly rent as numeric
    inputs

  - Calculates whether monthly rent is at least 1% of purchase price

- **Instant Output (Priority: Highest)**

  - Displays a prominent 'Deal' or 'No Deal' badge based on calculation

  - Accompanies result with a brief, plain-language explanation

- **UI/UX & Accessibility (Priority: High)**

  - Single-screen web interface with minimalist design

  - Responsive layout for all device sizes

  - Large inputs/buttons; minimal distractions

  - Keyboard navigation and screen reader support

  - Real-time input validation (no letters, negatives, etc.)

  - Immediate error messages for invalid input (e.g., zero or missing
    values)

- **Session Management & Flow (Priority: Medium)**

  - No login required

  - User can “reset” or immediately analyze another deal after each
    result

---

## User Experience

**Entry Point & First-Time User Experience**

- User arrives on a clean, distraction-free homepage

- Large hero section prompts for purchase price and monthly rent

- Placeholder examples guide input; call-to-action button labeled
  ‘Analyze Deal’

**Core Experience**

- **Step 1:** User enters purchase price and expected monthly rent

  - Numeric only, with clear label and placeholder text

  - Autoblur/autofocus for streamlined keyboard input

  - Inline error handling for invalid/blank/nonsensical data

- **Step 2:** User clicks or taps ‘Analyze Deal’

  - Button disabled until both fields are valid

  - Calculation occurs instantly without reload

- **Step 3:** Result is displayed in a large, visually-distinct badge
  (‘Deal’ in green, ‘No Deal’ in red)

  - Below the badge, a short sentence explains the outcome (e.g., “This
    deal meets the 1% rule; monthly rent is at least 1% of the purchase
    price.”)

- **Step 4:** User can click ‘Reset’ or “Analyze another deal” to start
  over

  - Inputs cleared or prefilled with previous values for rapid
    comparison

**Advanced Features & Edge Cases**

- Keyboard shortcuts for quick navigation (e.g., ‘Enter’ submits, ‘R’
  resets)

- Mobile optimization: large touch areas, responsive font sizes

- Error messages for $0, negative, or non-numeric entries

- Simple handling if a user reloads or returns: fields default to blank

**UI/UX Highlights**

- High color contrast for results badges

- Large font and input controls for mobile

- Minimalist, modern styling—think Robinhood’s simplicity

- Accessibility: All actions possible without mouse, ARIA labels for
  assistive tech

---

## Narrative

Nancy is just starting out in real estate investing. She’s heard of the
“1% rule” but shudders at the thought of wrangling a spreadsheet while
on the go. One afternoon at an open house, Nancy whips out her phone and
goes to our Multifamily Deal Calculator. She’s greeted by a crisp,
intuitive interface—just two fields and an ‘Analyze Deal’ button.
Punching in the purchase price and the rent she’s been quoted, she taps
the button and an animated green ‘Deal!’ badge fills her screen,
instantly backed by a one-line explanation. She can now move forward
confidently, knowing this property is worth researching further. Later
that day, she returns to the calculator to run three more addresses,
quickly narrowing her list. Nancy never needs to log in, deal with
errors, or wonder about arcane formulas. The tool becomes her go-to
first step in any property hunt, turning casual curiosity into
actionable insight—and connecting her to our brand’s wider investing
community.

---

## Success Metrics

### User-Centric Metrics

- Unique users per week/month

- Average number of deals analyzed per session

- Repeat user rate (%)

- User satisfaction/NPS score (via optional feedback prompt)

### Business Metrics

- Funnel rate: % of users clicking through to other platform
  products/features

- Email signups or lead captures (if/when enabled)

- Time-on-page and bounce rate for calculator landing page

### Technical Metrics

- Median response time for analysis (\<300ms)

- Error rate for input validation (\<1%)

- Uptime (\>99%)

### Tracking Plan

- Page load and session start

- Valid input submissions (analyze button click)

- Type of result (Deal vs. No Deal)

- Error events (invalid input, calculation failures)

- “Analyze another deal” clicks

- CTA engagement (clicks to broader platform or signup)

---

## Technical Considerations

### Technical Needs

- Lightweight web app (SPA or minimal server) for fast load

- All core logic on front-end for MVP

- Simple, scalable analytics/event-tracking integration (e.g.,
  segment.io, GA)

### Integration Points

- Basic analytics platform connection

- No login, no third-party property APIs in v1

### Data Storage & Privacy

- No user data stored for MVP; analytics anonymized

- No sensitive information or compliance concerns

### Scalability & Performance

- Built to support thousands of daily sessions without degradation

- Optimized for instant calculation and minimal payload

### Potential Challenges

- Ensuring perfect mobile usability

- Handling edge cases in numeric input (currency formatting, locale,
  etc.)

- Keeping experience frictionless without introducing unwanted features

---

## Milestones & Sequencing

### Project Estimate

- Extra-small: 2–4 days from start to launch

### Team Size & Composition

- 1 product owner (also acts as QA and business analyst)

- 1 engineer/designer (full-stack or front-end focused), could be a
  single individual

### Suggested Phases

**Phase 1: Design & Skeleton (Day 1)**

- Engineer/designer creates the minimalist UI structure

- Product owner reviews and signs off

**Phase 2: Core Logic & Output (Day 2)**

- Implement input handling, calculation logic, and result/explanation
  section

**Phase 3: Polish & QA (Day 3)**

- Add error handling (invalid inputs, usability edge cases)

- Test extensively on mobile, tablet, desktop

- Ensure accessibility and performance standards are met

**Phase 4: Deploy & Track (Day 4)**

- Deploy to production hosting; configure analytics/events tracking

- Monitor key success metrics

- Gather user feedback for v2 planning

Dependencies: None for MVP; entire project can be completed by a
two-person team (or one fast full-stack builder).
