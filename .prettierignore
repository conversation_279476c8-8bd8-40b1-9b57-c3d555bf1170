# Dependencies
node_modules/

# Build outputs
dist/
build/
.astro/

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Coverage reports
coverage/

# Test outputs
test-results/
playwright-report/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
.vscode/settings.json
.idea/

# Package manager files
package-lock.json
yarn.lock
pnpm-lock.yaml

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Generated files
*.min.js
*.min.css

# Documentation
CHANGELOG.md
