import js from "@eslint/js";
import typescript from "@typescript-eslint/eslint-plugin";
import typescriptParser from "@typescript-eslint/parser";
import react from "eslint-plugin-react";
import reactHooks from "eslint-plugin-react-hooks";
import prettier from "eslint-plugin-prettier";
import prettierConfig from "eslint-config-prettier";

export default [
	js.configs.recommended,
	{
		files: ["**/*.{js,jsx,ts,tsx}"],
		languageOptions: {
			parser: typescriptParser,
			parserOptions: {
				ecmaVersion: "latest",
				sourceType: "module",
				ecmaFeatures: {
					jsx: true,
				},
			},
			globals: {
				console: "readonly",
				process: "readonly",
				Buffer: "readonly",
				__dirname: "readonly",
				__filename: "readonly",
				global: "readonly",
				module: "readonly",
				require: "readonly",
				exports: "readonly",
				window: "readonly",
				document: "readonly",
				navigator: "readonly",
				localStorage: "readonly",
				sessionStorage: "readonly",
				HTMLElement: "readonly",
				HTMLInputElement: "readonly",
				Element: "readonly",
				Event: "readonly",
				EventTarget: "readonly",
				fetch: "readonly",
				URL: "readonly",
				URLSearchParams: "readonly",
				FormData: "readonly",
				Headers: "readonly",
				Request: "readonly",
				Response: "readonly",
				AbortController: "readonly",
				AbortSignal: "readonly",
				setTimeout: "readonly",
				clearTimeout: "readonly",
				setInterval: "readonly",
				clearInterval: "readonly",
				setImmediate: "readonly",
				clearImmediate: "readonly",
				queueMicrotask: "readonly",
				structuredClone: "readonly",
				crypto: "readonly",
				performance: "readonly",
				vi: "readonly",
				describe: "readonly",
				it: "readonly",
				test: "readonly",
				expect: "readonly",
				beforeEach: "readonly",
				afterEach: "readonly",
				beforeAll: "readonly",
				afterAll: "readonly",
			},
		},
		plugins: {
			"@typescript-eslint": typescript,
			react,
			"react-hooks": reactHooks,
			prettier,
		},
		rules: {
			...typescript.configs.recommended.rules,
			...react.configs.recommended.rules,
			...reactHooks.configs.recommended.rules,
			...prettierConfig.rules,
			"prettier/prettier": "error",
			"react/react-in-jsx-scope": "off",
			"react/prop-types": "off",
			"@typescript-eslint/no-unused-vars": [
				"error",
				{
					argsIgnorePattern: "^_",
					varsIgnorePattern: "^_",
				},
			],
			"@typescript-eslint/no-explicit-any": "warn",
			"@typescript-eslint/no-non-null-assertion": "warn",
			"no-console": "warn",
			"no-debugger": "error",
			"no-unused-vars": "off", // Use TypeScript version instead
		},
		settings: {
			react: {
				version: "detect",
			},
		},
	},
	{
		files: ["**/*.test.{js,jsx,ts,tsx}", "**/*.spec.{js,jsx,ts,tsx}"],
		rules: {
			"no-console": "off",
			"@typescript-eslint/no-explicit-any": "off",
		},
	},
	{
		ignores: [
			"node_modules/**",
			"dist/**",
			"build/**",
			".astro/**",
			"coverage/**",
			"playwright-report/**",
			"test-results/**",
		],
	},
];
