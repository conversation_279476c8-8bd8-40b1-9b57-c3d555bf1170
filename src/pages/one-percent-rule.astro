---
import Layout from '../layouts/Layout.astro';
import OnePercentCalculator from '../components/OnePercentCalculator';
import '../styles/global.css';
---

<Layout title="1% Rule Calculator - Real Estate Investment Tool">
  <main>
    <OnePercentCalculator client:load />
  </main>
</Layout>

<style>
  /* Custom animations */
  @keyframes fade-in {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-fade-in {
    animation: fade-in 0.3s ease-out;
  }

  /* Ensure proper focus styles for accessibility */
  input:focus {
    outline: none;
  }

  /* Custom button hover effects */
  button:disabled {
    transform: none !important;
  }

  /* Mobile optimizations */
  @media (max-width: 640px) {
    input {
      font-size: 16px; /* Prevents zoom on iOS */
    }
  }
</style>

<script>
  // Keyboard shortcuts for better UX
  document.addEventListener('keydown', (e) => {
    // 'R' key to reset (when not in input field)
    if (e.key === 'r' || e.key === 'R') {
      const activeElement = document.activeElement;
      if (activeElement?.tagName !== 'INPUT') {
        e.preventDefault();
        const resetButton = document.querySelector('button:contains("Analyze Another Deal")') as HTMLButtonElement;
        if (resetButton) {
          resetButton.click();
        }
      }
    }
  });

  // Analytics tracking (placeholder for future implementation)
  function trackEvent(eventName: string, properties?: Record<string, any>) {
    // This would integrate with your analytics platform
    console.log('Analytics Event:', eventName, properties);
  }

  // Track page load
  trackEvent('page_load', {
    page: 'one_percent_calculator',
    timestamp: new Date().toISOString()
  });

  // Track form submissions
  document.addEventListener('submit', (e) => {
    if (e.target instanceof HTMLFormElement) {
      trackEvent('calculation_submitted', {
        timestamp: new Date().toISOString()
      });
    }
  });
</script>
