@import "tailwindcss";
@import "@radix-ui/themes/styles.css";

/* Theme configuration for retro monotone styling */
:root {
	--font-mono:
		ui-monospace, SFMono-Regular, "SF Mono", <PERSON><PERSON><PERSON>, "Liberation Mono", Menlo,
		monospace;
}

/* Apply retro monotone theme */
.radix-themes {
	--default-font-family: var(--font-mono);
	--heading-font-family: var(--font-mono);
	--code-font-family: var(--font-mono);
}

/* Global body styling with mono font */
body {
	font-family: var(--font-mono);
	background-color: var(--gray-1);
	color: var(--gray-12);
}

/* Custom styles for Deal Analyzer */
.deal-analyzer-container {
	max-width: 1200px;
	margin: 0 auto;
}

.deal-analyzer-card {
	border-radius: 12px;
	box-shadow:
		0 4px 6px -1px rgba(0, 0, 0, 0.1),
		0 2px 4px -1px rgba(0, 0, 0, 0.06);
	transition: all 0.2s ease-in-out;
}

.deal-analyzer-card:hover {
	box-shadow:
		0 10px 15px -3px rgba(0, 0, 0, 0.1),
		0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.deal-analyzer-input {
	border-radius: 8px;
	border: 1px solid var(--gray-6);
	transition: border-color 0.2s ease-in-out;
}

.deal-analyzer-input:focus {
	border-color: var(--blue-8);
	box-shadow: 0 0 0 2px var(--blue-4);
}

.deal-analyzer-button {
	border-radius: 8px;
	font-weight: 600;
	transition: all 0.2s ease-in-out;
}

.deal-analyzer-button:hover {
	transform: translateY(-1px);
}

.deal-analyzer-result-card {
	background: linear-gradient(135deg, var(--gray-1) 0%, var(--gray-2) 100%);
	border: 1px solid var(--gray-4);
	border-radius: 12px;
}

.deal-analyzer-metric {
	text-align: center;
	padding: 1rem;
	border-radius: 8px;
	background: var(--gray-1);
	border: 1px solid var(--gray-3);
}

.deal-analyzer-metric-value {
	font-size: 1.5rem;
	font-weight: 700;
	color: var(--gray-12);
	margin-bottom: 0.25rem;
}

.deal-analyzer-metric-label {
	font-size: 0.875rem;
	color: var(--gray-11);
	font-weight: 500;
}

.deal-analyzer-section {
	margin-bottom: 2rem;
}

.deal-analyzer-section-title {
	font-size: 1.25rem;
	font-weight: 600;
	color: var(--gray-12);
	margin-bottom: 1rem;
	padding-bottom: 0.5rem;
	border-bottom: 2px solid var(--gray-4);
}

.deal-analyzer-analysis-box {
	background: var(--gray-2);
	border-left: 4px solid var(--blue-6);
	padding: 1rem;
	border-radius: 0 8px 8px 0;
	margin-top: 1rem;
}

.deal-analyzer-formatted-value {
	font-size: 0.875rem;
	color: var(--gray-10);
	margin-top: 0.25rem;
	font-weight: 500;
}

/* Responsive improvements */
@media (max-width: 768px) {
	.deal-analyzer-container {
		padding: 1rem;
	}

	.deal-analyzer-metric {
		padding: 0.75rem;
	}

	.deal-analyzer-metric-value {
		font-size: 1.25rem;
	}

	.deal-analyzer-section-title {
		font-size: 1.125rem;
	}
}

/* Loading animation */
.deal-analyzer-loading {
	opacity: 0.7;
	pointer-events: none;
}

/* Success/Error states */
.deal-analyzer-success {
	border-left-color: var(--green-6);
}

.deal-analyzer-warning {
	border-left-color: var(--yellow-6);
}

.deal-analyzer-error {
	border-left-color: var(--red-6);
}

/* Grid improvements for better mobile experience */
.deal-analyzer-grid {
	display: grid;
	gap: 1rem;
}

@media (min-width: 640px) {
	.deal-analyzer-grid {
		grid-template-columns: repeat(2, 1fr);
	}
}

@media (min-width: 1024px) {
	.deal-analyzer-grid {
		grid-template-columns: repeat(3, 1fr);
	}
}
