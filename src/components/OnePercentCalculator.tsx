import React, { useState } from "react";
import {
	Theme,
	Container,
	Heading,
	Text,
	Card,
	TextField,
	Button,
	Callout,
	Separator,
	Flex,
	Box,
} from "@radix-ui/themes";

import {
	calculateOnePercentRule,
	validateInput,
	parseCurrency,
	formatCurrency,
	type OnePercentRuleResult,
} from "../lib/onePercentRule";
import { useUrlState } from "../hooks/useUrlState";

interface FormData {
	purchasePrice: string;
	monthlyRent: string;
	[key: string]: string;
}

export default function OnePercentCalculator() {
	const [formData, setFormData] = useUrlState<FormData>({
		purchasePrice: "",
		monthlyRent: "",
	});

	const [result, setResult] = useState<OnePercentRuleResult | null>(null);
	const [error, setError] = useState<string>("");
	const [isCalculating, setIsCalculating] = useState(false);

	const handleInputChange = (field: keyof FormData, value: string) => {
		// Only allow digits (and optional decimal point)
		const rawValue = value.replace(/[^\d.]/g, "");

		if (result) setResult(null);
		if (error) setError("");
		setFormData({ [field]: rawValue });
	};

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();

		// Clear previous results and errors
		setResult(null);
		setError("");
		setIsCalculating(true);

		try {
			// Parse input values
			const purchasePrice = parseCurrency(formData.purchasePrice);
			const monthlyRent = parseCurrency(formData.monthlyRent);

			// Validate inputs
			const validationError = validateInput(purchasePrice, monthlyRent);
			if (validationError) {
				setError(validationError);
				return;
			}

			// Simulate brief loading for better UX
			await new Promise((resolve) => setTimeout(resolve, 300));

			// Calculate result
			const calculationResult = calculateOnePercentRule({
				purchasePrice,
				monthlyRent,
			});

			setResult(calculationResult);
		} catch {
			setError("An unexpected error occurred. Please try again.");
		} finally {
			setIsCalculating(false);
		}
	};

	const handleReset = () => {
		setFormData({ purchasePrice: "", monthlyRent: "" });
		setResult(null);
		setError("");
	};

	const isFormValid =
		parseCurrency(formData.purchasePrice) > 0 &&
		parseCurrency(formData.monthlyRent) > 0;

	return (
		<Theme>
			<Container size="2" p="4">
				<Flex direction="column" gap="6">
					{/* Header */}
					<Box style={{ textAlign: "center" }}>
						<Heading size="8" mb="2">
							1% Rule Calculator
						</Heading>
						<Text color="gray">
							Quickly determine if a property meets the 1% rule for rental
							income.
						</Text>
					</Box>

					{/* Calculator Form */}
					<Card>
						<form onSubmit={handleSubmit}>
							<Flex direction="column" gap="4">
								{/* Purchase Price Input */}
								<Box>
									<Text as="label" size="2" weight="bold" mb="1">
										Purchase Price
									</Text>
									<TextField.Root
										placeholder="500000"
										value={formData.purchasePrice}
										onChange={(e) =>
											handleInputChange("purchasePrice", e.target.value)
										}
									></TextField.Root>
									{formData.purchasePrice &&
										!isNaN(parseCurrency(formData.purchasePrice)) && (
											<Text size="1" color="gray">
												{formatCurrency(parseCurrency(formData.purchasePrice))}
											</Text>
										)}
								</Box>

								{/* Monthly Rent Input */}
								<Box>
									<Text as="label" size="2" weight="bold" mb="1">
										Monthly Rent
									</Text>
									<TextField.Root
										placeholder="5000"
										value={formData.monthlyRent}
										onChange={(e) =>
											handleInputChange("monthlyRent", e.target.value)
										}
									></TextField.Root>
									{formData.monthlyRent &&
										!isNaN(parseCurrency(formData.monthlyRent)) && (
											<Text size="1" color="gray">
												{formatCurrency(parseCurrency(formData.monthlyRent))}
											</Text>
										)}
								</Box>

								{/* Error Message */}
								{error && (
									<Callout.Root color="red">
										<Callout.Icon>⚠️</Callout.Icon>
										<Callout.Text>{error}</Callout.Text>
									</Callout.Root>
								)}

								{/* Submit Button */}
								<Button
									type="submit"
									disabled={!isFormValid || isCalculating}
									size="3"
								>
									{isCalculating ? "Analyzing..." : "Analyze Deal"}
								</Button>
							</Flex>
						</form>
					</Card>

					{/* Results */}
					{result && (
						<Card>
							<Flex direction="column" gap="4">
								{/* Result Badge */}
								<Box style={{ textAlign: "center" }}>
									<Callout.Root
										color={result.recommendation === "DEAL" ? "green" : "red"}
										size="2"
									>
										<Callout.Icon>
											{result.recommendation === "DEAL" ? "✅" : "❌"}
										</Callout.Icon>
										<Callout.Text weight="bold">
											{result.recommendation === "DEAL" ? "DEAL!" : "NO DEAL"}
										</Callout.Text>
									</Callout.Root>
								</Box>

								{/* Explanation */}
								<Box p="3" style={{ backgroundColor: "var(--gray-2)" }}>
									<Text>{result.explanation}</Text>
								</Box>

								<Separator />

								{/* Additional Info */}
								<Flex gap="4" direction={{ initial: "column", sm: "row" }}>
									<Card style={{ flex: 1, textAlign: "center" }}>
										<Text size="1" color="gray" weight="bold">
											Rent to Price Ratio
										</Text>
										<Heading size="6">{result.rentToPrice.toFixed(2)}%</Heading>
									</Card>
									<Card style={{ flex: 1, textAlign: "center" }}>
										<Text size="1" color="gray" weight="bold">
											Target Threshold
										</Text>
										<Heading size="6">1.00%</Heading>
									</Card>
								</Flex>

								{/* Reset Button */}
								<Button variant="soft" onClick={handleReset}>
									Analyze Another Deal
								</Button>
							</Flex>
						</Card>
					)}

					{/* Info Section */}
					<Card>
						<Heading size="4" mb="3">
							About the 1% Rule
						</Heading>
						<Text color="gray">
							The 1% rule is a quick screening tool used by real estate
							investors. It suggests that a property&apos;s monthly rent should
							be at least 1% of its purchase price to potentially be a good
							investment. While not a guarantee of profitability, it helps
							filter out properties that are unlikely to generate positive cash
							flow.
						</Text>
					</Card>
				</Flex>
			</Container>
		</Theme>
	);
}
