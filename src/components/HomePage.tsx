import React from "react";
import {
	Container,
	Heading,
	Text,
	Card,
	<PERSON>ton,
	Flex,
	Box,
	Grid,
} from "@radix-ui/themes";
import { ThemeProvider } from "./ThemeProvider";

export default function HomePage() {
	return (
		<ThemeProvider accentColor="gray" grayColor="gray" radius="medium">
			<Container size="4" p="4">
				<Flex direction="column" gap="8">
					{/* Hero Section */}
					<Box style={{ textAlign: "center" }}>
						<Heading size="9" mb="4">
							Real Estate Investment Tools
						</Heading>
						<Text
							size="4"
							color="gray"
							style={{ maxWidth: "600px", margin: "0 auto" }}
						>
							Professional-grade calculators and analysis tools for real estate
							investors. Make smarter investment decisions with instant
							calculations.
						</Text>
					</Box>

					{/* Tools Grid */}
					<Grid columns={{ initial: "1", md: "2" }} gap="6">
						{/* 1% Rule Calculator Card */}
						<Card>
							<Flex direction="column" gap="4">
								<Box style={{ textAlign: "center" }}>
									<Text size="8" style={{ fontSize: "3rem" }}>
										🏠
									</Text>
									<Heading size="6" mt="2" mb="2">
										1% Rule Calculator
									</Heading>
									<Text color="gray" size="2">
										Instant property screening tool for quick deal analysis.
									</Text>
								</Box>

								<Flex direction="column" gap="2">
									<Flex align="center" gap="2">
										<Text size="2">✓</Text>
										<Text size="2" color="gray">
											Instant analysis results
										</Text>
									</Flex>
									<Flex align="center" gap="2">
										<Text size="2">✓</Text>
										<Text size="2" color="gray">
											Mobile optimized interface
										</Text>
									</Flex>
									<Flex align="center" gap="2">
										<Text size="2">✓</Text>
										<Text size="2" color="gray">
											No registration required
										</Text>
									</Flex>
								</Flex>

								<Button asChild>
									<a href="/dealtools/one-percent-rule">Try Calculator →</a>
								</Button>
							</Flex>
						</Card>

						{/* Deal Analyzer Card */}
						<Card>
							<Flex direction="column" gap="4">
								<Box style={{ textAlign: "center" }}>
									<Text size="8" style={{ fontSize: "3rem" }}>
										📊
									</Text>
									<Heading size="6" mt="2" mb="2">
										Full Deal Analyzer
									</Heading>
									<Text color="gray" size="2">
										Comprehensive investment analysis with cash flow and
										projections.
									</Text>
								</Box>

								<Flex direction="column" gap="2">
									<Flex align="center" gap="2">
										<Text size="2">✓</Text>
										<Text size="2" color="gray">
											Complete financial analysis
										</Text>
									</Flex>
									<Flex align="center" gap="2">
										<Text size="2">✓</Text>
										<Text size="2" color="gray">
											Expense tracking tools
										</Text>
									</Flex>
									<Flex align="center" gap="2">
										<Text size="2">✓</Text>
										<Text size="2" color="gray">
											Investment projections
										</Text>
									</Flex>
								</Flex>

								<Button variant="soft" asChild>
									<a href="/dealtools/deal-analyzer">Try Analyzer →</a>
								</Button>
							</Flex>
						</Card>
					</Grid>

					{/* Features Section */}
					<Card>
						<Flex direction="column" gap="6">
							<Heading size="6" style={{ textAlign: "center" }}>
								Why Choose Our Tools?
							</Heading>

							<Grid columns={{ initial: "1", md: "3" }} gap="6">
								<Box style={{ textAlign: "center" }}>
									<Text size="8" style={{ fontSize: "2rem" }}>
										⚡
									</Text>
									<Heading size="4" mt="2" mb="2">
										Lightning Fast
									</Heading>
									<Text color="gray" size="2">
										Get instant results without complex spreadsheets or manual
										calculations.
									</Text>
								</Box>

								<Box style={{ textAlign: "center" }}>
									<Text size="8" style={{ fontSize: "2rem" }}>
										📱
									</Text>
									<Heading size="4" mt="2" mb="2">
										Mobile Ready
									</Heading>
									<Text color="gray" size="2">
										Use anywhere, anytime on any device with responsive design.
									</Text>
								</Box>

								<Box style={{ textAlign: "center" }}>
									<Text size="8" style={{ fontSize: "2rem" }}>
										🎯
									</Text>
									<Heading size="4" mt="2" mb="2">
										Accurate Results
									</Heading>
									<Text color="gray" size="2">
										Professional-grade calculations trusted by real estate
										investors.
									</Text>
								</Box>
							</Grid>
						</Flex>
					</Card>
				</Flex>
			</Container>
		</ThemeProvider>
	);
}
