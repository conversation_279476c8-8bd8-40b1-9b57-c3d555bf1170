import React, { useState } from "react";
import { z } from "zod";

import {
	calculateComparativeAnalysis,
	type ComparativeAnalysisInput,
	type ComparativeAnalysisResult,
} from "../lib/dealCalculations";
import { useUrlState } from "../hooks/useUrlState";

// Zod schema for form data validation
const ComparativeFormDataSchema = z.object({
	// Current Property State
	currentPurchasePrice: z.string().min(1, "Purchase price is required"),
	currentNOI: z.string().min(1, "Current NOI is required"),

	// Projected Property State
	projectedNOI: z.string().min(1, "Projected NOI is required"),
	improvementCost: z.string().min(1, "Improvement cost is required"),
	projectedTimelineMonths: z.string().min(1, "Timeline is required"),
	marketCapRate: z.string().min(1, "Market cap rate is required"),
});

// Zod schema for parsed numeric values
const ComparativeAnalysisInputSchema = z.object({
	current: z.object({
		purchasePrice: z
			.number()
			.positive("Purchase price must be greater than $0"),
		currentNOI: z.number().positive("Current NOI must be greater than $0"),
		currentGrossIncome: z
			.number()
			.nonnegative("Gross income cannot be negative"),
		currentExpenses: z.number().nonnegative("Expenses cannot be negative"),
	}),
	projected: z.object({
		projectedNOI: z.number().positive("Projected NOI must be greater than $0"),
		projectedGrossIncome: z
			.number()
			.nonnegative("Projected gross income cannot be negative"),
		projectedExpenses: z
			.number()
			.nonnegative("Projected expenses cannot be negative"),
		improvementCost: z
			.number()
			.nonnegative("Improvement cost cannot be negative"),
		projectedTimelineMonths: z
			.number()
			.positive("Timeline must be greater than 0 months"),
		marketCapRate: z
			.number()
			.positive("Market cap rate must be greater than 0%"),
	}),
});

type ComparativeFormData = z.infer<typeof ComparativeFormDataSchema>;

// Helper function to validate individual field values
const validateFieldValue = (
	field: keyof ComparativeFormData,
	value: string,
): string | null => {
	if (!value.trim()) return null; // Don't validate empty fields

	const numericValue = parseFloat(value);
	if (isNaN(numericValue)) return "Must be a valid number";

	switch (field) {
		case "currentPurchasePrice":
		case "currentNOI":
		case "projectedNOI":
		case "projectedTimelineMonths":
		case "marketCapRate":
			return numericValue <= 0 ? "Must be greater than 0" : null;
		case "improvementCost":
			return numericValue < 0 ? "Cannot be negative" : null;
		default:
			return null;
	}
};

export default function DealAnalyzer() {
	const [formData, setFormData] = useUrlState<ComparativeFormData>({
		// Current Property State
		currentPurchasePrice: "",
		currentNOI: "",

		// Projected Property State
		projectedNOI: "",
		improvementCost: "",
		projectedTimelineMonths: "",
		marketCapRate: "",
	});

	const [result, setResult] = useState<ComparativeAnalysisResult | null>(null);
	const [error, setError] = useState<string>("");
	const [isCalculating, setIsCalculating] = useState(false);
	const [fieldErrors, setFieldErrors] = useState<
		Partial<Record<keyof ComparativeFormData, string>>
	>({});

	const handleInputChange = (
		field: keyof ComparativeFormData,
		value: string,
	) => {
		// Only allow digits and decimal points
		const rawValue = value.replace(/[^\d.]/g, "");

		// Validate that it's a valid number format (optional validation)
		if (rawValue && !/^\d*\.?\d*$/.test(rawValue)) {
			return; // Don't update if invalid format
		}

		// Validate field value and update field errors
		const fieldError = validateFieldValue(field, rawValue);
		setFieldErrors((prev) => ({
			...prev,
			[field]: fieldError || undefined,
		}));

		if (result) setResult(null);
		if (error) setError("");
		setFormData({ [field]: rawValue });
	};

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();

		setResult(null);
		setError("");
		setIsCalculating(true);

		try {
			// Validate all fields and collect errors
			const newFieldErrors: Partial<Record<keyof ComparativeFormData, string>> =
				{};

			// Validate each field individually
			Object.keys(formData).forEach((key) => {
				const fieldKey = key as keyof ComparativeFormData;
				const fieldError = validateFieldValue(fieldKey, formData[fieldKey]);
				if (fieldError) {
					newFieldErrors[fieldKey] = fieldError;
				}
			});

			// Also validate required fields
			const formValidation = ComparativeFormDataSchema.safeParse(formData);
			if (!formValidation.success) {
				formValidation.error.errors.forEach((error) => {
					const fieldName = error.path[0] as keyof ComparativeFormData;
					if (!newFieldErrors[fieldName]) {
						newFieldErrors[fieldName] = error.message;
					}
				});
			}

			// Update field errors state
			setFieldErrors(newFieldErrors);

			// If there are any field errors, don't proceed with calculation
			if (Object.keys(newFieldErrors).length > 0) {
				setError("Please fix the errors above before submitting.");
				return;
			}

			// Parse and validate numeric values
			const input: ComparativeAnalysisInput = {
				current: {
					purchasePrice: parseFloat(formData.currentPurchasePrice),
					currentNOI: parseFloat(formData.currentNOI),
					// Set gross income and expenses to NOI for calculation compatibility
					currentGrossIncome: parseFloat(formData.currentNOI),
					currentExpenses: 0,
				},
				projected: {
					projectedNOI: parseFloat(formData.projectedNOI),
					// Set gross income and expenses to NOI for calculation compatibility
					projectedGrossIncome: parseFloat(formData.projectedNOI),
					projectedExpenses: 0,
					improvementCost: parseFloat(formData.improvementCost),
					projectedTimelineMonths: parseFloat(formData.projectedTimelineMonths),
					marketCapRate: parseFloat(formData.marketCapRate),
				},
			};

			// Validate parsed numeric values with Zod
			const inputValidation = ComparativeAnalysisInputSchema.safeParse(input);
			if (!inputValidation.success) {
				const firstError = inputValidation.error.errors[0];
				setError(firstError.message);
				return;
			}

			// Simulate brief loading for better UX
			await new Promise((resolve) => setTimeout(resolve, 300));

			// Calculate comparative analysis results
			const analysisResult = calculateComparativeAnalysis(inputValidation.data);
			setResult(analysisResult);
		} catch {
			setError("An unexpected error occurred. Please try again.");
		} finally {
			setIsCalculating(false);
		}
	};

	const handleReset = () => {
		setFormData({
			// Current Property State
			currentPurchasePrice: "",
			currentNOI: "",

			// Projected Property State
			projectedNOI: "",
			improvementCost: "",
			projectedTimelineMonths: "",
			marketCapRate: "",
		});
		setResult(null);
		setError("");
		setFieldErrors({});
	};

	// Always allow form submission - we'll show validation errors to guide users
	const isFormValid = true;

	return (
		<div>
			{/* Header */}
			<div>
				<h1>Comparative Deal Analyzer</h1>
				<p>
					Compare current vs projected property performance to determine if
					improvements are financially worthwhile.
				</p>
			</div>

			{/* Calculator Form */}
			<div>
				<form onSubmit={handleSubmit}>
					{/* Current Property State */}
					<div>
						<h2>Current Property State</h2>
						<div>
							<div>
								<label>Purchase Price</label>
								<input
									type="text"
									placeholder="500000"
									value={formData.currentPurchasePrice}
									onChange={(e) =>
										handleInputChange("currentPurchasePrice", e.target.value)
									}
								/>
								{fieldErrors.currentPurchasePrice && (
									<p style={{ color: "red", fontSize: "0.875rem" }}>
										{fieldErrors.currentPurchasePrice}
									</p>
								)}
								{formData.currentPurchasePrice &&
									!fieldErrors.currentPurchasePrice && (
										<p>
											{new Intl.NumberFormat("en-US", {
												style: "currency",
												currency: "USD",
												minimumFractionDigits: 0,
												maximumFractionDigits: 0,
											}).format(parseFloat(formData.currentPurchasePrice) || 0)}
										</p>
									)}
							</div>
							<div>
								<label>Current NOI</label>
								<input
									type="text"
									placeholder="50000"
									value={formData.currentNOI}
									onChange={(e) =>
										handleInputChange("currentNOI", e.target.value)
									}
								/>
								{fieldErrors.currentNOI && (
									<p style={{ color: "red", fontSize: "0.875rem" }}>
										{fieldErrors.currentNOI}
									</p>
								)}
								{formData.currentNOI && !fieldErrors.currentNOI && (
									<p>
										{new Intl.NumberFormat("en-US", {
											style: "currency",
											currency: "USD",
											minimumFractionDigits: 0,
											maximumFractionDigits: 0,
										}).format(parseFloat(formData.currentNOI) || 0)}
									</p>
								)}
							</div>
						</div>
					</div>

					<hr />

					{/* Projected Property State */}
					<div>
						<h2>Projected Property State (After Improvements)</h2>
						<div>
							<div>
								<label>Projected NOI</label>
								<input
									type="text"
									placeholder="65000"
									value={formData.projectedNOI}
									onChange={(e) =>
										handleInputChange("projectedNOI", e.target.value)
									}
								/>
								{fieldErrors.projectedNOI && (
									<p style={{ color: "red", fontSize: "0.875rem" }}>
										{fieldErrors.projectedNOI}
									</p>
								)}
								{formData.projectedNOI && !fieldErrors.projectedNOI && (
									<p>
										{new Intl.NumberFormat("en-US", {
											style: "currency",
											currency: "USD",
											minimumFractionDigits: 0,
											maximumFractionDigits: 0,
										}).format(parseFloat(formData.projectedNOI) || 0)}
									</p>
								)}
							</div>
							<div>
								<label>Improvement Cost</label>
								<input
									type="text"
									placeholder="25000"
									value={formData.improvementCost}
									onChange={(e) =>
										handleInputChange("improvementCost", e.target.value)
									}
								/>
								{fieldErrors.improvementCost && (
									<p style={{ color: "red", fontSize: "0.875rem" }}>
										{fieldErrors.improvementCost}
									</p>
								)}
								{formData.improvementCost && !fieldErrors.improvementCost && (
									<p>
										{new Intl.NumberFormat("en-US", {
											style: "currency",
											currency: "USD",
											minimumFractionDigits: 0,
											maximumFractionDigits: 0,
										}).format(parseFloat(formData.improvementCost) || 0)}
									</p>
								)}
							</div>
							<div>
								<label>Timeline (Months)</label>
								<input
									type="text"
									placeholder="6"
									value={formData.projectedTimelineMonths}
									onChange={(e) =>
										handleInputChange("projectedTimelineMonths", e.target.value)
									}
								/>
								{fieldErrors.projectedTimelineMonths && (
									<p style={{ color: "red", fontSize: "0.875rem" }}>
										{fieldErrors.projectedTimelineMonths}
									</p>
								)}
								{formData.projectedTimelineMonths &&
									!fieldErrors.projectedTimelineMonths && (
										<p>
											{parseFloat(formData.projectedTimelineMonths) || 0} months
										</p>
									)}
							</div>
							<div>
								<label>Market Cap Rate (%)</label>
								<input
									type="text"
									placeholder="6.5"
									value={formData.marketCapRate}
									onChange={(e) =>
										handleInputChange("marketCapRate", e.target.value)
									}
								/>
								{fieldErrors.marketCapRate && (
									<p style={{ color: "red", fontSize: "0.875rem" }}>
										{fieldErrors.marketCapRate}
									</p>
								)}
								{formData.marketCapRate && !fieldErrors.marketCapRate && (
									<p>{(parseFloat(formData.marketCapRate) || 0).toFixed(2)}%</p>
								)}
							</div>
						</div>
					</div>

					{/* Error Message */}
					{error && (
						<div>
							<span>⚠️</span>
							<p>{error}</p>
						</div>
					)}

					{/* Submit Button */}
					<button type="submit" disabled={!isFormValid || isCalculating}>
						{isCalculating ? "Comparing..." : "Compare Properties"}
					</button>
				</form>
			</div>

			{/* Comparative Analysis Results */}
			{result && (
				<div>
					{/* Property Metrics Comparison */}
					<div>
						<h2>Property Metrics Comparison</h2>
						<table>
							<thead>
								<tr>
									<th>Metric</th>
									<th>Current</th>
									<th>Projected</th>
									<th>Difference</th>
									<th>% Change</th>
								</tr>
							</thead>
							<tbody>
								{/* NOI Row */}
								<tr>
									<td>Net Operating Income</td>
									<td>
										{new Intl.NumberFormat("en-US", {
											style: "currency",
											currency: "USD",
											minimumFractionDigits: 0,
										}).format(result.noiComparison.current)}
									</td>
									<td>
										{new Intl.NumberFormat("en-US", {
											style: "currency",
											currency: "USD",
											minimumFractionDigits: 0,
										}).format(result.noiComparison.projected)}
									</td>
									<td>
										{result.noiComparison.difference >= 0 ? "+" : ""}
										{new Intl.NumberFormat("en-US", {
											style: "currency",
											currency: "USD",
											minimumFractionDigits: 0,
										}).format(result.noiComparison.difference)}
									</td>
									<td>
										{result.noiComparison.difference >= 0 ? "+" : ""}
										{result.noiComparison.percentageChange.toFixed(1)}%
									</td>
								</tr>

								{/* Cap Rate Row */}
								<tr>
									<td>Cap Rate</td>
									<td>{result.capRateComparison.current.toFixed(2)}%</td>
									<td>{result.capRateComparison.projected.toFixed(2)}%</td>
									<td>
										{result.capRateComparison.difference >= 0 ? "+" : ""}
										{result.capRateComparison.difference.toFixed(2)}%
									</td>
									<td>
										{result.capRateComparison.difference >= 0 ? "+" : ""}
										{result.capRateComparison.percentageChange.toFixed(1)}%
									</td>
								</tr>
							</tbody>
						</table>
					</div>

					{/* Investment ROI Analysis */}
					<div>
						<h2>Investment ROI Analysis</h2>
						<div>
							<div>
								<h3>Annual ROI</h3>
								<p>{result.roiAnalysis.roi.toFixed(1)}%</p>
								<p>Target: 15%+ Excellent, 8%+ Good</p>
							</div>
							<div>
								<h3>Payback Period</h3>
								<p>{result.roiAnalysis.paybackPeriodYears.toFixed(1)} years</p>
								<p>Time to recover investment</p>
							</div>
							<div>
								<h3>10-Year NPV</h3>
								<p>
									{new Intl.NumberFormat("en-US", {
										style: "currency",
										currency: "USD",
										minimumFractionDigits: 0,
									}).format(result.roiAnalysis.netPresentValue)}
								</p>
								<p>Net present value over 10 years</p>
							</div>
						</div>
					</div>

					{/* Property Valuation Impact */}
					<div>
						<h2>Property Valuation Impact</h2>
						<div>
							<div>
								<h3>Current Property Value</h3>
								<p>
									{new Intl.NumberFormat("en-US", {
										style: "currency",
										currency: "USD",
										minimumFractionDigits: 0,
									}).format(result.propertyValuation.currentValue)}
								</p>
								<p>Based on current NOI and market cap rate</p>
							</div>
							<div>
								<h3>Projected Property Value</h3>
								<p>
									{new Intl.NumberFormat("en-US", {
										style: "currency",
										currency: "USD",
										minimumFractionDigits: 0,
									}).format(result.propertyValuation.projectedValue)}
								</p>
								<p>After improvements and NOI increase</p>
							</div>
						</div>
						<div>
							<h3>Equity Growth</h3>
							<p>
								+
								{new Intl.NumberFormat("en-US", {
									style: "currency",
									currency: "USD",
									minimumFractionDigits: 0,
								}).format(result.propertyValuation.valueIncrease)}
							</p>
							<p>Increase in property value from improvements</p>
						</div>
					</div>

					{/* Cash Flow Analysis */}
					<div>
						<h2>Cash Flow Analysis</h2>
						<div>
							<div>
								<h3>Monthly Cash Flow Increase</h3>
								<p>
									+
									{new Intl.NumberFormat("en-US", {
										style: "currency",
										currency: "USD",
										minimumFractionDigits: 0,
									}).format(result.cashFlowAnalysis.monthlyIncrease)}
								</p>
								<p>Additional monthly income</p>
							</div>
							<div>
								<h3>Annual Cash Flow Increase</h3>
								<p>
									+
									{new Intl.NumberFormat("en-US", {
										style: "currency",
										currency: "USD",
										minimumFractionDigits: 0,
									}).format(result.cashFlowAnalysis.annualIncrease)}
								</p>
								<p>Additional yearly income</p>
							</div>
							<div>
								<h3>5-Year Cash Flow Benefit</h3>
								<p>
									+
									{new Intl.NumberFormat("en-US", {
										style: "currency",
										currency: "USD",
										minimumFractionDigits: 0,
									}).format(result.cashFlowAnalysis.cumulativeIncrease5Years)}
								</p>
								<p>Total additional income over 5 years</p>
							</div>
						</div>
					</div>

					{/* Reset Button */}
					<div>
						<button onClick={handleReset}>Reset Calculator</button>
					</div>
				</div>
			)}

			{/* Information Card */}
			<div>
				<h2>About This Calculator</h2>
				<p>
					This comparative deal analyzer helps you evaluate whether property
					improvements are financially worthwhile by comparing current vs
					projected performance across multiple metrics.
				</p>
				<div>
					<p>
						• <strong>Property Metrics Comparison:</strong> Side-by-side
						comparison of NOI and cap rates
					</p>
					<p>
						• <strong>Investment ROI Analysis:</strong> Calculates annual ROI,
						payback period, and 10-year net present value
					</p>
					<p>
						• <strong>Property Valuation Impact:</strong> Shows how improvements
						affect property value and equity growth
					</p>
					<p>
						• <strong>Cash Flow Improvement:</strong> Analyzes monthly, annual,
						and long-term cash flow benefits
					</p>
				</div>
			</div>
		</div>
	);
}
