import React, { useState } from "react";
import {
	Container,
	Heading,
	Text,
	Card,
	TextField,
	Button,
	Callout,
	Separator,
	Flex,
	Box,
	Grid,
} from "@radix-ui/themes";
import { ThemeProvider } from "./ThemeProvider";

import {
	calculateCapRate,
	calculateImprovementROI,
	calculateRecessionSensitivity,
	type DealAnalysisInput,
	type DealAnalysisResult,
} from "../lib/dealCalculations";
import { useUrlState } from "../hooks/useUrlState";

interface DealFormData {
	currentPurchasePrice: string;
	currentNOI: string;
	improvementCost: string;
	noiIncrease: string;
	marketCapRate: string;
	recessionOccupancyRate: string;
	[key: string]: string;
}

export default function DealAnalyzer() {
	const [formData, setFormData] = useUrlState<DealFormData>({
		currentPurchasePrice: "",
		currentNOI: "",
		improvementCost: "",
		noiIncrease: "",
		marketCapRate: "",
		recessionOccupancyRate: "",
	});

	const [result, setResult] = useState<DealAnalysisResult | null>(null);
	const [error, setError] = useState<string>("");
	const [isCalculating, setIsCalculating] = useState(false);

	const handleInputChange = (field: keyof DealFormData, value: string) => {
		// Only allow digits and decimal points
		const rawValue = value.replace(/[^\d.]/g, "");

		if (result) setResult(null);
		if (error) setError("");
		setFormData({ [field]: rawValue });
	};

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();

		setResult(null);
		setError("");
		setIsCalculating(true);

		try {
			// Parse input values
			const input: DealAnalysisInput = {
				currentPurchasePrice: parseFloat(formData.currentPurchasePrice) || 0,
				currentNOI: parseFloat(formData.currentNOI) || 0,
				improvementCost: parseFloat(formData.improvementCost) || 0,
				noiIncrease: parseFloat(formData.noiIncrease) || 0,
				marketCapRate: parseFloat(formData.marketCapRate) || 0,
				recessionOccupancyRate:
					parseFloat(formData.recessionOccupancyRate) || 0,
			};

			// Validate inputs
			if (input.currentPurchasePrice <= 0) {
				setError("Current purchase price must be greater than $0");
				return;
			}
			if (input.currentNOI <= 0) {
				setError("Current NOI must be greater than $0");
				return;
			}
			if (input.marketCapRate <= 0) {
				setError("Market cap rate must be greater than 0%");
				return;
			}

			// Simulate brief loading for better UX
			await new Promise((resolve) => setTimeout(resolve, 300));

			// Calculate results
			const analysisResult = calculateCapRate(input);
			const improvementAnalysis = calculateImprovementROI(input);
			const recessionSensitivity = calculateRecessionSensitivity(input);

			setResult({
				capRate: analysisResult,
				improvements: improvementAnalysis,
				recession: recessionSensitivity,
			});
		} catch {
			setError("An unexpected error occurred. Please try again.");
		} finally {
			setIsCalculating(false);
		}
	};

	const handleReset = () => {
		setFormData({
			currentPurchasePrice: "",
			currentNOI: "",
			improvementCost: "",
			noiIncrease: "",
			marketCapRate: "",
			recessionOccupancyRate: "",
		});
		setResult(null);
		setError("");
	};

	const isFormValid =
		parseFloat(formData.currentPurchasePrice) > 0 &&
		parseFloat(formData.currentNOI) > 0 &&
		parseFloat(formData.marketCapRate) > 0;

	return (
		<ThemeProvider accentColor="gray" grayColor="gray" radius="medium">
			<Container size="2" p="4">
				<Flex direction="column" gap="6">
					{/* Header */}
					<Box style={{ textAlign: "center" }}>
						<Heading size="8" mb="2">
							Full Deal Analyzer
						</Heading>
						<Text color="gray">
							Comprehensive investment analysis with improvement ROI and
							recession sensitivity testing.
						</Text>
					</Box>

					{/* Calculator Form */}
					<Card>
						<form onSubmit={handleSubmit}>
							<Flex direction="column" gap="4">
								{/* Current Property Metrics */}
								<Box>
									<Heading size="4" mb="3">
										Current Property Metrics
									</Heading>
									<Grid columns={{ initial: "1", sm: "2" }} gap="3">
										<Box>
											<Text as="label" size="2" weight="bold" mb="1">
												Purchase Price
											</Text>
											<TextField.Root
												placeholder="500000"
												value={formData.currentPurchasePrice}
												onChange={(e) =>
													handleInputChange(
														"currentPurchasePrice",
														e.target.value,
													)
												}
											/>
											{formData.currentPurchasePrice && (
												<Text size="1" color="gray">
													{new Intl.NumberFormat("en-US", {
														style: "currency",
														currency: "USD",
														minimumFractionDigits: 0,
														maximumFractionDigits: 0,
													}).format(
														parseFloat(formData.currentPurchasePrice) || 0,
													)}
												</Text>
											)}
										</Box>
										<Box>
											<Text as="label" size="2" weight="bold" mb="1">
												Current NOI
											</Text>
											<TextField.Root
												placeholder="50000"
												value={formData.currentNOI}
												onChange={(e) =>
													handleInputChange("currentNOI", e.target.value)
												}
											/>
											{formData.currentNOI && (
												<Text size="1" color="gray">
													{new Intl.NumberFormat("en-US", {
														style: "currency",
														currency: "USD",
														minimumFractionDigits: 0,
														maximumFractionDigits: 0,
													}).format(parseFloat(formData.currentNOI) || 0)}
												</Text>
											)}
										</Box>
									</Grid>
								</Box>

								<Separator />

								{/* Improvement Analysis */}
								<Box>
									<Heading size="4" mb="3">
										Improvement Analysis
									</Heading>
									<Grid columns={{ initial: "1", sm: "3" }} gap="3">
										<Box>
											<Text as="label" size="2" weight="bold" mb="1">
												Improvement Cost
											</Text>
											<TextField.Root
												placeholder="25000"
												value={formData.improvementCost}
												onChange={(e) =>
													handleInputChange("improvementCost", e.target.value)
												}
											/>
											{formData.improvementCost && (
												<Text size="1" color="gray">
													{new Intl.NumberFormat("en-US", {
														style: "currency",
														currency: "USD",
														minimumFractionDigits: 0,
														maximumFractionDigits: 0,
													}).format(parseFloat(formData.improvementCost) || 0)}
												</Text>
											)}
										</Box>
										<Box>
											<Text as="label" size="2" weight="bold" mb="1">
												Annual NOI Increase
											</Text>
											<TextField.Root
												placeholder="5000"
												value={formData.noiIncrease}
												onChange={(e) =>
													handleInputChange("noiIncrease", e.target.value)
												}
											/>
											{formData.noiIncrease && (
												<Text size="1" color="gray">
													{new Intl.NumberFormat("en-US", {
														style: "currency",
														currency: "USD",
														minimumFractionDigits: 0,
														maximumFractionDigits: 0,
													}).format(parseFloat(formData.noiIncrease) || 0)}
												</Text>
											)}
										</Box>
										<Box>
											<Text as="label" size="2" weight="bold" mb="1">
												Market Cap Rate (%)
											</Text>
											<TextField.Root
												placeholder="6.5"
												value={formData.marketCapRate}
												onChange={(e) =>
													handleInputChange("marketCapRate", e.target.value)
												}
											/>
											{formData.marketCapRate && (
												<Text size="1" color="gray">
													{(parseFloat(formData.marketCapRate) || 0).toFixed(2)}
													%
												</Text>
											)}
										</Box>
									</Grid>
								</Box>

								<Separator />

								{/* Recession Sensitivity */}
								<Box>
									<Heading size="4" mb="3">
										Recession Sensitivity
									</Heading>
									<Box>
										<Text as="label" size="2" weight="bold" mb="1">
											Recession Occupancy Rate (%)
										</Text>
										<TextField.Root
											placeholder="85"
											value={formData.recessionOccupancyRate}
											onChange={(e) =>
												handleInputChange(
													"recessionOccupancyRate",
													e.target.value,
												)
											}
										/>
										{formData.recessionOccupancyRate && (
											<Text size="1" color="gray">
												{(
													parseFloat(formData.recessionOccupancyRate) || 0
												).toFixed(1)}
												% (Historical average during recessions)
											</Text>
										)}
									</Box>
								</Box>

								{/* Error Message */}
								{error && (
									<Callout.Root color="red">
										<Callout.Icon>⚠️</Callout.Icon>
										<Callout.Text>{error}</Callout.Text>
									</Callout.Root>
								)}

								{/* Submit Button */}
								<Button
									type="submit"
									disabled={!isFormValid || isCalculating}
									size="3"
								>
									{isCalculating ? "Analyzing..." : "Analyze Deal"}
								</Button>
							</Flex>
						</form>
					</Card>

					{/* Results */}
					{result && (
						<Flex direction="column" gap="4">
							{/* Cap Rate Analysis */}
							<Card>
								<Heading size="4" mb="3">
									Cap Rate Analysis
								</Heading>
								<Grid columns={{ initial: "1", sm: "2" }} gap="4">
									<Box style={{ textAlign: "center" }}>
										<Text size="2" color="gray" weight="bold">
											Current Cap Rate
										</Text>
										<Heading size="6">
											{result.capRate.currentCapRate.toFixed(2)}%
										</Heading>
									</Box>
									<Box style={{ textAlign: "center" }}>
										<Text size="2" color="gray" weight="bold">
											Market Cap Rate
										</Text>
										<Heading size="6">
											{result.capRate.marketCapRate.toFixed(2)}%
										</Heading>
									</Box>
								</Grid>
								<Box p="3" style={{ backgroundColor: "var(--gray-2)" }}>
									<Text>{result.capRate.analysis}</Text>
								</Box>
							</Card>

							{/* Improvement Analysis */}
							<Card>
								<Heading size="4" mb="3">
									Improvement ROI Analysis
								</Heading>
								<Grid columns={{ initial: "1", sm: "2" }} gap="4">
									<Box style={{ textAlign: "center" }}>
										<Text size="2" color="gray" weight="bold">
											ROI on Improvements
										</Text>
										<Heading size="6">
											{result.improvements.roi.toFixed(1)}%
										</Heading>
									</Box>
									<Box style={{ textAlign: "center" }}>
										<Text size="2" color="gray" weight="bold">
											New Property Value
										</Text>
										<Heading size="6">
											{new Intl.NumberFormat("en-US", {
												style: "currency",
												currency: "USD",
												minimumFractionDigits: 0,
												maximumFractionDigits: 0,
											}).format(result.improvements.newPropertyValue)}
										</Heading>
									</Box>
								</Grid>
								<Box p="3" style={{ backgroundColor: "var(--gray-2)" }}>
									<Text>{result.improvements.analysis}</Text>
								</Box>
							</Card>

							{/* Recession Sensitivity */}
							<Card>
								<Heading size="4" mb="3">
									Recession Sensitivity Test
								</Heading>
								<Grid columns={{ initial: "1", sm: "2" }} gap="4">
									<Box style={{ textAlign: "center" }}>
										<Text size="2" color="gray" weight="bold">
											Recession NOI
										</Text>
										<Heading size="6">
											{new Intl.NumberFormat("en-US", {
												style: "currency",
												currency: "USD",
												minimumFractionDigits: 0,
												maximumFractionDigits: 0,
											}).format(result.recession.recessionNOI)}
										</Heading>
									</Box>
									<Box style={{ textAlign: "center" }}>
										<Text size="2" color="gray" weight="bold">
											Recession Cap Rate
										</Text>
										<Heading size="6">
											{result.recession.recessionCapRate.toFixed(2)}%
										</Heading>
									</Box>
								</Grid>
								<Box p="3" style={{ backgroundColor: "var(--gray-2)" }}>
									<Text>{result.recession.analysis}</Text>
								</Box>
							</Card>

							{/* Reset Button */}
							<Button variant="soft" onClick={handleReset}>
								Analyze Another Deal
							</Button>
						</Flex>
					)}

					{/* Info Section */}
					<Card>
						<Heading size="4" mb="3">
							About the Full Deal Analyzer
						</Heading>
						<Text color="gray">
							This comprehensive analysis tool evaluates your real estate
							investment from multiple angles:
						</Text>
						<Flex direction="column" gap="2" mt="3">
							<Text size="2">
								• <strong>Cap Rate Analysis:</strong> Compares your
								property&apos;s cap rate to market rates
							</Text>
							<Text size="2">
								• <strong>Improvement ROI:</strong> Calculates return on
								investment for property improvements
							</Text>
							<Text size="2">
								• <strong>Recession Sensitivity:</strong> Tests how your
								investment performs during economic downturns
							</Text>
						</Flex>
					</Card>
				</Flex>
			</Container>
		</ThemeProvider>
	);
}
