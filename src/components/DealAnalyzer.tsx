import React, { useState } from "react";

import {
	calculateComparativeAnalysis,
	type ComparativeAnalysisInput,
	type ComparativeAnalysisResult,
} from "../lib/dealCalculations";
import { useUrlState } from "../hooks/useUrlState";

interface ComparativeFormData {
	// Current Property State
	currentPurchasePrice: string;
	currentNOI: string;

	// Projected Property State
	projectedNOI: string;
	improvementCost: string;
	projectedTimelineMonths: string;
	marketCapRate: string;

	[key: string]: string;
}

export default function DealAnalyzer() {
	const [formData, setFormData] = useUrlState<ComparativeFormData>({
		// Current Property State
		currentPurchasePrice: "",
		currentNOI: "",

		// Projected Property State
		projectedNOI: "",
		improvementCost: "",
		projectedTimelineMonths: "",
		marketCapRate: "",
	});

	const [result, setResult] = useState<ComparativeAnalysisResult | null>(null);
	const [error, setError] = useState<string>("");
	const [isCalculating, setIsCalculating] = useState(false);

	const handleInputChange = (
		field: keyof ComparativeFormData,
		value: string,
	) => {
		// Only allow digits and decimal points
		const rawValue = value.replace(/[^\d.]/g, "");

		if (result) setResult(null);
		if (error) setError("");
		setFormData({ [field]: rawValue });
	};

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();

		setResult(null);
		setError("");
		setIsCalculating(true);

		try {
			// Parse input values
			const input: ComparativeAnalysisInput = {
				current: {
					purchasePrice: parseFloat(formData.currentPurchasePrice) || 0,
					currentNOI: parseFloat(formData.currentNOI) || 0,
					// Set gross income and expenses to NOI for calculation compatibility
					currentGrossIncome: parseFloat(formData.currentNOI) || 0,
					currentExpenses: 0,
				},
				projected: {
					projectedNOI: parseFloat(formData.projectedNOI) || 0,
					// Set gross income and expenses to NOI for calculation compatibility
					projectedGrossIncome: parseFloat(formData.projectedNOI) || 0,
					projectedExpenses: 0,
					improvementCost: parseFloat(formData.improvementCost) || 0,
					projectedTimelineMonths:
						parseFloat(formData.projectedTimelineMonths) || 0,
					marketCapRate: parseFloat(formData.marketCapRate) || 0,
				},
			};

			// Validate inputs
			if (input.current.purchasePrice <= 0) {
				setError("Current purchase price must be greater than $0");
				return;
			}
			if (input.current.currentNOI <= 0) {
				setError("Current NOI must be greater than $0");
				return;
			}
			if (input.projected.projectedNOI <= 0) {
				setError("Projected NOI must be greater than $0");
				return;
			}
			if (input.projected.improvementCost < 0) {
				setError("Improvement cost cannot be negative");
				return;
			}
			if (input.projected.projectedTimelineMonths <= 0) {
				setError("Project timeline must be greater than 0 months");
				return;
			}
			if (input.projected.marketCapRate <= 0) {
				setError("Market cap rate must be greater than 0%");
				return;
			}

			// Simulate brief loading for better UX
			await new Promise((resolve) => setTimeout(resolve, 300));

			// Calculate comparative analysis results
			const analysisResult = calculateComparativeAnalysis(input);
			setResult(analysisResult);
		} catch {
			setError("An unexpected error occurred. Please try again.");
		} finally {
			setIsCalculating(false);
		}
	};

	const handleReset = () => {
		setFormData({
			// Current Property State
			currentPurchasePrice: "",
			currentNOI: "",

			// Projected Property State
			projectedNOI: "",
			improvementCost: "",
			projectedTimelineMonths: "",
			marketCapRate: "",
		});
		setResult(null);
		setError("");
	};

	const isFormValid =
		parseFloat(formData.currentPurchasePrice) > 0 &&
		parseFloat(formData.currentNOI) > 0 &&
		parseFloat(formData.projectedNOI) > 0 &&
		parseFloat(formData.marketCapRate) > 0;

	return (
		<div>
			{/* Header */}
			<div>
				<h1>Comparative Deal Analyzer</h1>
				<p>
					Compare current vs projected property performance to determine if
					improvements are financially worthwhile.
				</p>
			</div>

			{/* Calculator Form */}
			<div>
				<form onSubmit={handleSubmit}>
					{/* Current Property State */}
					<div>
						<h2>Current Property State</h2>
						<div>
							<div>
								<label>Purchase Price</label>
								<input
									type="text"
									placeholder="500000"
									value={formData.currentPurchasePrice}
									onChange={(e) =>
										handleInputChange("currentPurchasePrice", e.target.value)
									}
								/>
								{formData.currentPurchasePrice && (
									<p>
										{new Intl.NumberFormat("en-US", {
											style: "currency",
											currency: "USD",
											minimumFractionDigits: 0,
											maximumFractionDigits: 0,
										}).format(parseFloat(formData.currentPurchasePrice) || 0)}
									</p>
								)}
							</div>
							<div>
								<label>Current NOI</label>
								<input
									type="text"
									placeholder="50000"
									value={formData.currentNOI}
									onChange={(e) =>
										handleInputChange("currentNOI", e.target.value)
									}
								/>
								{formData.currentNOI && (
									<p>
										{new Intl.NumberFormat("en-US", {
											style: "currency",
											currency: "USD",
											minimumFractionDigits: 0,
											maximumFractionDigits: 0,
										}).format(parseFloat(formData.currentNOI) || 0)}
									</p>
								)}
							</div>
						</div>
					</div>

					<hr />

					{/* Projected Property State */}
					<div>
						<h2>Projected Property State (After Improvements)</h2>
						<div>
							<div>
								<label>Projected NOI</label>
								<input
									type="text"
									placeholder="65000"
									value={formData.projectedNOI}
									onChange={(e) =>
										handleInputChange("projectedNOI", e.target.value)
									}
								/>
								{formData.projectedNOI && (
									<p>
										{new Intl.NumberFormat("en-US", {
											style: "currency",
											currency: "USD",
											minimumFractionDigits: 0,
											maximumFractionDigits: 0,
										}).format(parseFloat(formData.projectedNOI) || 0)}
									</p>
								)}
							</div>
							<div>
								<label>Improvement Cost</label>
								<input
									type="text"
									placeholder="25000"
									value={formData.improvementCost}
									onChange={(e) =>
										handleInputChange("improvementCost", e.target.value)
									}
								/>
								{formData.improvementCost && (
									<p>
										{new Intl.NumberFormat("en-US", {
											style: "currency",
											currency: "USD",
											minimumFractionDigits: 0,
											maximumFractionDigits: 0,
										}).format(parseFloat(formData.improvementCost) || 0)}
									</p>
								)}
							</div>
							<div>
								<label>Timeline (Months)</label>
								<input
									type="text"
									placeholder="6"
									value={formData.projectedTimelineMonths}
									onChange={(e) =>
										handleInputChange("projectedTimelineMonths", e.target.value)
									}
								/>
								{formData.projectedTimelineMonths && (
									<p>
										{parseFloat(formData.projectedTimelineMonths) || 0} months
									</p>
								)}
							</div>
							<div>
								<label>Market Cap Rate (%)</label>
								<input
									type="text"
									placeholder="6.5"
									value={formData.marketCapRate}
									onChange={(e) =>
										handleInputChange("marketCapRate", e.target.value)
									}
								/>
								{formData.marketCapRate && (
									<p>{(parseFloat(formData.marketCapRate) || 0).toFixed(2)}%</p>
								)}
							</div>
						</div>
					</div>

					{/* Error Message */}
					{error && (
						<div>
							<span>⚠️</span>
							<p>{error}</p>
						</div>
					)}

					{/* Submit Button */}
					<button type="submit" disabled={!isFormValid || isCalculating}>
						{isCalculating ? "Comparing..." : "Compare Properties"}
					</button>
				</form>
			</div>

			{/* Comparative Analysis Results */}
			{result && (
				<div>
					{/* Property Metrics Comparison */}
					<div>
						<h2>Property Metrics Comparison</h2>
						<table>
							<thead>
								<tr>
									<th>Metric</th>
									<th>Current</th>
									<th>Projected</th>
									<th>Difference</th>
									<th>% Change</th>
								</tr>
							</thead>
							<tbody>
								{/* NOI Row */}
								<tr>
									<td>Net Operating Income</td>
									<td>
										{new Intl.NumberFormat("en-US", {
											style: "currency",
											currency: "USD",
											minimumFractionDigits: 0,
										}).format(result.noiComparison.current)}
									</td>
									<td>
										{new Intl.NumberFormat("en-US", {
											style: "currency",
											currency: "USD",
											minimumFractionDigits: 0,
										}).format(result.noiComparison.projected)}
									</td>
									<td>
										{result.noiComparison.difference >= 0 ? "+" : ""}
										{new Intl.NumberFormat("en-US", {
											style: "currency",
											currency: "USD",
											minimumFractionDigits: 0,
										}).format(result.noiComparison.difference)}
									</td>
									<td>
										{result.noiComparison.difference >= 0 ? "+" : ""}
										{result.noiComparison.percentageChange.toFixed(1)}%
									</td>
								</tr>

								{/* Cap Rate Row */}
								<tr>
									<td>Cap Rate</td>
									<td>{result.capRateComparison.current.toFixed(2)}%</td>
									<td>{result.capRateComparison.projected.toFixed(2)}%</td>
									<td>
										{result.capRateComparison.difference >= 0 ? "+" : ""}
										{result.capRateComparison.difference.toFixed(2)}%
									</td>
									<td>
										{result.capRateComparison.difference >= 0 ? "+" : ""}
										{result.capRateComparison.percentageChange.toFixed(1)}%
									</td>
								</tr>
							</tbody>
						</table>
					</div>

					{/* Investment ROI Analysis */}
					<div>
						<h2>Investment ROI Analysis</h2>
						<div>
							<div>
								<h3>Annual ROI</h3>
								<p>{result.roiAnalysis.roi.toFixed(1)}%</p>
								<p>Target: 15%+ Excellent, 8%+ Good</p>
							</div>
							<div>
								<h3>Payback Period</h3>
								<p>{result.roiAnalysis.paybackPeriodYears.toFixed(1)} years</p>
								<p>Time to recover investment</p>
							</div>
							<div>
								<h3>10-Year NPV</h3>
								<p>
									{new Intl.NumberFormat("en-US", {
										style: "currency",
										currency: "USD",
										minimumFractionDigits: 0,
									}).format(result.roiAnalysis.netPresentValue)}
								</p>
								<p>Net present value over 10 years</p>
							</div>
						</div>
					</div>

					{/* Property Valuation Impact */}
					<div>
						<h2>Property Valuation Impact</h2>
						<div>
							<div>
								<h3>Current Property Value</h3>
								<p>
									{new Intl.NumberFormat("en-US", {
										style: "currency",
										currency: "USD",
										minimumFractionDigits: 0,
									}).format(result.propertyValuation.currentValue)}
								</p>
								<p>Based on current NOI and market cap rate</p>
							</div>
							<div>
								<h3>Projected Property Value</h3>
								<p>
									{new Intl.NumberFormat("en-US", {
										style: "currency",
										currency: "USD",
										minimumFractionDigits: 0,
									}).format(result.propertyValuation.projectedValue)}
								</p>
								<p>After improvements and NOI increase</p>
							</div>
						</div>
						<div>
							<h3>Equity Growth</h3>
							<p>
								+
								{new Intl.NumberFormat("en-US", {
									style: "currency",
									currency: "USD",
									minimumFractionDigits: 0,
								}).format(result.propertyValuation.valueIncrease)}
							</p>
							<p>Increase in property value from improvements</p>
						</div>
					</div>

					{/* Cash Flow Analysis */}
					<div>
						<h2>Cash Flow Analysis</h2>
						<div>
							<div>
								<h3>Monthly Cash Flow Increase</h3>
								<p>
									+
									{new Intl.NumberFormat("en-US", {
										style: "currency",
										currency: "USD",
										minimumFractionDigits: 0,
									}).format(result.cashFlowAnalysis.monthlyIncrease)}
								</p>
								<p>Additional monthly income</p>
							</div>
							<div>
								<h3>Annual Cash Flow Increase</h3>
								<p>
									+
									{new Intl.NumberFormat("en-US", {
										style: "currency",
										currency: "USD",
										minimumFractionDigits: 0,
									}).format(result.cashFlowAnalysis.annualIncrease)}
								</p>
								<p>Additional yearly income</p>
							</div>
							<div>
								<h3>5-Year Cash Flow Benefit</h3>
								<p>
									+
									{new Intl.NumberFormat("en-US", {
										style: "currency",
										currency: "USD",
										minimumFractionDigits: 0,
									}).format(result.cashFlowAnalysis.cumulativeIncrease5Years)}
								</p>
								<p>Total additional income over 5 years</p>
							</div>
						</div>
					</div>

					{/* Reset Button */}
					<div>
						<button onClick={handleReset}>Reset Calculator</button>
					</div>
				</div>
			)}

			{/* Information Card */}
			<div>
				<h2>About This Calculator</h2>
				<p>
					This comparative deal analyzer helps you evaluate whether property
					improvements are financially worthwhile by comparing current vs
					projected performance across multiple metrics.
				</p>
				<div>
					<p>
						• <strong>Property Metrics Comparison:</strong> Side-by-side
						comparison of NOI and cap rates
					</p>
					<p>
						• <strong>Investment ROI Analysis:</strong> Calculates annual ROI,
						payback period, and 10-year net present value
					</p>
					<p>
						• <strong>Property Valuation Impact:</strong> Shows how improvements
						affect property value and equity growth
					</p>
					<p>
						• <strong>Cash Flow Improvement:</strong> Analyzes monthly, annual,
						and long-term cash flow benefits
					</p>
				</div>
			</div>
		</div>
	);
}
