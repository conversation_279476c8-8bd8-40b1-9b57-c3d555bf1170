import React, { useState } from "react";

import {
	calculateComparativeAnalysis,
	type ComparativeAnalysisInput,
	type ComparativeAnalysisResult,
	// Legacy imports for backward compatibility
	calculateCapRate,
	calculateImprovementROI,
	calculateRecessionSensitivity,
	type DealAnalysisInput,
	type DealAnalysisResult,
} from "../lib/dealCalculations";
import { useUrlState } from "../hooks/useUrlState";

interface ComparativeFormData {
	// Current Property State
	currentPurchasePrice: string;
	currentNOI: string;

	// Projected Property State
	projectedNOI: string;
	improvementCost: string;
	projectedTimelineMonths: string;
	marketCapRate: string;

	[key: string]: string;
}

export default function DealAnalyzer() {
	const [formData, setFormData] = useUrlState<ComparativeFormData>({
		// Current Property State
		currentPurchasePrice: "",
		currentNOI: "",

		// Projected Property State
		projectedNOI: "",
		improvementCost: "",
		projectedTimelineMonths: "",
		marketCapRate: "",
	});

	const [result, setResult] = useState<ComparativeAnalysisResult | null>(null);
	const [error, setError] = useState<string>("");
	const [isCalculating, setIsCalculating] = useState(false);

	const handleInputChange = (
		field: keyof ComparativeFormData,
		value: string,
	) => {
		// Only allow digits and decimal points
		const rawValue = value.replace(/[^\d.]/g, "");

		if (result) setResult(null);
		if (error) setError("");
		setFormData({ [field]: rawValue });
	};

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();

		setResult(null);
		setError("");
		setIsCalculating(true);

		try {
			// Parse input values
			const input: ComparativeAnalysisInput = {
				current: {
					purchasePrice: parseFloat(formData.currentPurchasePrice) || 0,
					currentNOI: parseFloat(formData.currentNOI) || 0,
					// Set gross income and expenses to NOI for calculation compatibility
					currentGrossIncome: parseFloat(formData.currentNOI) || 0,
					currentExpenses: 0,
				},
				projected: {
					projectedNOI: parseFloat(formData.projectedNOI) || 0,
					// Set gross income and expenses to NOI for calculation compatibility
					projectedGrossIncome: parseFloat(formData.projectedNOI) || 0,
					projectedExpenses: 0,
					improvementCost: parseFloat(formData.improvementCost) || 0,
					projectedTimelineMonths:
						parseFloat(formData.projectedTimelineMonths) || 0,
					marketCapRate: parseFloat(formData.marketCapRate) || 0,
				},
			};

			// Validate inputs
			if (input.current.purchasePrice <= 0) {
				setError("Current purchase price must be greater than $0");
				return;
			}
			if (input.current.currentNOI <= 0) {
				setError("Current NOI must be greater than $0");
				return;
			}
			if (input.projected.projectedNOI <= 0) {
				setError("Projected NOI must be greater than $0");
				return;
			}
			if (input.projected.improvementCost < 0) {
				setError("Improvement cost cannot be negative");
				return;
			}
			if (input.projected.projectedTimelineMonths <= 0) {
				setError("Project timeline must be greater than 0 months");
				return;
			}
			if (input.projected.marketCapRate <= 0) {
				setError("Market cap rate must be greater than 0%");
				return;
			}

			// Simulate brief loading for better UX
			await new Promise((resolve) => setTimeout(resolve, 300));

			// Calculate comparative analysis results
			const analysisResult = calculateComparativeAnalysis(input);
			setResult(analysisResult);
		} catch {
			setError("An unexpected error occurred. Please try again.");
		} finally {
			setIsCalculating(false);
		}
	};

	const handleReset = () => {
		setFormData({
			// Current Property State
			currentPurchasePrice: "",
			currentNOI: "",

			// Projected Property State
			projectedNOI: "",
			improvementCost: "",
			projectedTimelineMonths: "",
			marketCapRate: "",
		});
		setResult(null);
		setError("");
	};

	const isFormValid =
		parseFloat(formData.currentPurchasePrice) > 0 &&
		parseFloat(formData.currentNOI) > 0 &&
		parseFloat(formData.projectedNOI) > 0 &&
		parseFloat(formData.marketCapRate) > 0;

	return (
		<div className="max-w-4xl mx-auto p-6 space-y-8 bg-gray-950 min-h-screen">
			{/* Header */}
			<div className="text-center">
				<h1 className="text-4xl font-bold mb-4 text-gray-100">
					Comparative Deal Analyzer
				</h1>
				<p className="text-gray-400 font-mono">
					Compare current vs projected property performance to determine if
					improvements are financially worthwhile.
				</p>
			</div>

			{/* Calculator Form */}
			<div className="bg-gray-900 border border-gray-700 rounded-lg p-6">
				<form onSubmit={handleSubmit} className="space-y-6">
					{/* Current Property State */}
					<div>
						<h2 className="text-xl font-bold mb-4 text-gray-100">
							Current Property State
						</h2>
						<div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
							<div>
								<label className="block text-sm font-bold mb-2 text-gray-200 font-mono">
									Purchase Price
								</label>
								<input
									type="text"
									placeholder="500000"
									value={formData.currentPurchasePrice}
									onChange={(e) =>
										handleInputChange("currentPurchasePrice", e.target.value)
									}
									className="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-md text-gray-100 font-mono placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent"
								/>
								{formData.currentPurchasePrice && (
									<p className="text-xs text-gray-400 mt-1 font-mono">
										{new Intl.NumberFormat("en-US", {
											style: "currency",
											currency: "USD",
											minimumFractionDigits: 0,
											maximumFractionDigits: 0,
										}).format(parseFloat(formData.currentPurchasePrice) || 0)}
									</p>
								)}
							</div>
							<div>
								<label className="block text-sm font-bold mb-2 text-gray-200 font-mono">
									Current NOI
								</label>
								<input
									type="text"
									placeholder="50000"
									value={formData.currentNOI}
									onChange={(e) =>
										handleInputChange("currentNOI", e.target.value)
									}
									className="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-md text-gray-100 font-mono placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent"
								/>
								{formData.currentNOI && (
									<p className="text-xs text-gray-400 mt-1 font-mono">
										{new Intl.NumberFormat("en-US", {
											style: "currency",
											currency: "USD",
											minimumFractionDigits: 0,
											maximumFractionDigits: 0,
										}).format(parseFloat(formData.currentNOI) || 0)}
									</p>
								)}
							</div>
							<div>
								<label className="block text-sm font-bold mb-2 text-gray-200 font-mono">
									Timeline (Months)
								</label>
								<input
									type="text"
									placeholder="6"
									value={formData.projectedTimelineMonths}
									onChange={(e) =>
										handleInputChange("projectedTimelineMonths", e.target.value)
									}
									className="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-md text-gray-100 font-mono placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent"
								/>
								{formData.projectedTimelineMonths && (
									<p className="text-xs text-gray-400 mt-1 font-mono">
										{parseFloat(formData.projectedTimelineMonths) || 0} months
									</p>
								)}
							</div>
							<div>
								<label className="block text-sm font-bold mb-2 text-gray-200 font-mono">
									Market Cap Rate (%)
								</label>
								<input
									type="text"
									placeholder="6.5"
									value={formData.marketCapRate}
									onChange={(e) =>
										handleInputChange("marketCapRate", e.target.value)
									}
									className="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-md text-gray-100 font-mono placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent"
								/>
								{formData.marketCapRate && (
									<p className="text-xs text-gray-400 mt-1 font-mono">
										{(parseFloat(formData.marketCapRate) || 0).toFixed(2)}%
									</p>
								)}
							</div>
						</div>
					</div>

					{/* Error Message */}
					{error && (
						<div className="bg-red-900 border border-red-700 rounded-lg p-4 flex items-center space-x-3">
							<span className="text-red-400 text-lg">⚠️</span>
							<p className="text-red-200 font-mono">{error}</p>
						</div>
					)}

					{/* Submit Button */}
					<button
						type="submit"
						disabled={!isFormValid || isCalculating}
						className="w-full px-6 py-4 bg-teal-600 hover:bg-teal-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white font-bold rounded-lg transition-colors duration-200 font-mono"
					>
						{isCalculating ? "Comparing..." : "Compare Properties"}
					</button>
				</form>
			</div>

			{/* Comparative Analysis Results */}
			{result && (
				<div className="space-y-6">
					{/* Property Metrics Comparison */}
					<div className="bg-gray-900 border border-gray-700 rounded-lg p-6">
						<h2 className="text-xl font-bold mb-4 text-gray-100">
							Property Metrics Comparison
						</h2>
						<div className="overflow-x-auto">
							<table className="w-full border-collapse font-mono text-sm">
								<thead>
									<tr className="bg-gray-800 border-b-2 border-gray-600">
										<th className="p-3 text-left font-bold text-gray-100">
											Metric
										</th>
										<th className="p-3 text-right font-bold text-gray-100">
											Current
										</th>
										<th className="p-3 text-right font-bold text-gray-100">
											Projected
										</th>
										<th className="p-3 text-right font-bold text-gray-100">
											Difference
										</th>
										<th className="p-3 text-right font-bold text-gray-100">
											% Change
										</th>
									</tr>
								</thead>
								<tbody>
									{/* NOI Row */}
									<tr className="border-b border-gray-700 bg-gray-800">
										<td className="p-3 font-medium text-gray-100">
											Net Operating Income
										</td>
										<td className="p-3 text-right font-medium text-gray-300">
											{new Intl.NumberFormat("en-US", {
												style: "currency",
												currency: "USD",
												minimumFractionDigits: 0,
											}).format(result.noiComparison.current)}
										</td>
										<td className="p-3 text-right font-medium text-gray-300">
											{new Intl.NumberFormat("en-US", {
												style: "currency",
												currency: "USD",
												minimumFractionDigits: 0,
											}).format(result.noiComparison.projected)}
										</td>
										<td
											className={`p-3 text-right font-semibold ${
												result.noiComparison.difference >= 0
													? "text-green-400"
													: "text-red-400"
											}`}
										>
											{result.noiComparison.difference >= 0 ? "+" : ""}
											{new Intl.NumberFormat("en-US", {
												style: "currency",
												currency: "USD",
												minimumFractionDigits: 0,
											}).format(result.noiComparison.difference)}
										</td>
										<td
											className={`p-3 text-right font-semibold ${
												result.noiComparison.difference >= 0
													? "text-green-400"
													: "text-red-400"
											}`}
										>
											{result.noiComparison.difference >= 0 ? "+" : ""}
											{result.noiComparison.percentageChange.toFixed(1)}%
										</td>
									</tr>

									{/* Cap Rate Row */}
									<tr className="border-b border-gray-700 bg-gray-850">
										<td className="p-3 font-medium text-gray-100">Cap Rate</td>
										<td className="p-3 text-right font-medium text-gray-300">
											{result.capRateComparison.current.toFixed(2)}%
										</td>
										<td className="p-3 text-right font-medium text-gray-300">
											{result.capRateComparison.projected.toFixed(2)}%
										</td>
										<td
											className={`p-3 text-right font-semibold ${
												result.capRateComparison.difference >= 0
													? "text-green-400"
													: "text-red-400"
											}`}
										>
											{result.capRateComparison.difference >= 0 ? "+" : ""}
											{result.capRateComparison.difference.toFixed(2)}%
										</td>
										<td
											className={`p-3 text-right font-semibold ${
												result.capRateComparison.difference >= 0
													? "text-green-400"
													: "text-red-400"
											}`}
										>
											{result.capRateComparison.difference >= 0 ? "+" : ""}
											{result.capRateComparison.percentageChange.toFixed(1)}%
										</td>
									</tr>
								</tbody>
							</table>
						</div>
					</div>

					{/* Investment ROI Analysis */}
					<div className="bg-gray-900 border border-gray-700 rounded-lg p-6">
						<h2 className="text-xl font-bold mb-4 text-gray-100">
							Investment ROI Analysis
						</h2>
						<div className="grid grid-cols-1 md:grid-cols-3 gap-6">
							<div className="bg-gray-800 rounded-lg p-4">
								<h3 className="text-lg font-semibold mb-2 text-gray-200">
									Annual ROI
								</h3>
								<p
									className={`text-2xl font-bold font-mono ${
										result.investmentROI.annualROI >= 15
											? "text-green-400"
											: result.investmentROI.annualROI >= 8
												? "text-yellow-400"
												: "text-red-400"
									}`}
								>
									{result.investmentROI.annualROI.toFixed(1)}%
								</p>
								<p className="text-xs text-gray-400 mt-1 font-mono">
									Target: 15%+ Excellent, 8%+ Good
								</p>
							</div>
							<div className="bg-gray-800 rounded-lg p-4">
								<h3 className="text-lg font-semibold mb-2 text-gray-200">
									Payback Period
								</h3>
								<p className="text-2xl font-bold font-mono text-gray-100">
									{result.investmentROI.paybackPeriodYears.toFixed(1)} years
								</p>
								<p className="text-xs text-gray-400 mt-1 font-mono">
									Time to recover investment
								</p>
							</div>
							<div className="bg-gray-800 rounded-lg p-4">
								<h3 className="text-lg font-semibold mb-2 text-gray-200">
									10-Year NPV
								</h3>
								<p
									className={`text-2xl font-bold font-mono ${
										result.investmentROI.tenYearNPV >= 0
											? "text-green-400"
											: "text-red-400"
									}`}
								>
									{new Intl.NumberFormat("en-US", {
										style: "currency",
										currency: "USD",
										minimumFractionDigits: 0,
									}).format(result.investmentROI.tenYearNPV)}
								</p>
								<p className="text-xs text-gray-400 mt-1 font-mono">
									Net present value over 10 years
								</p>
							</div>
						</div>
					</div>

					{/* Property Valuation Impact */}
					<div className="bg-gray-900 border border-gray-700 rounded-lg p-6">
						<h2 className="text-xl font-bold mb-4 text-gray-100">
							Property Valuation Impact
						</h2>
						<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
							<div className="bg-gray-800 rounded-lg p-4">
								<h3 className="text-lg font-semibold mb-2 text-gray-200">
									Current Property Value
								</h3>
								<p className="text-2xl font-bold font-mono text-gray-100">
									{new Intl.NumberFormat("en-US", {
										style: "currency",
										currency: "USD",
										minimumFractionDigits: 0,
									}).format(result.propertyValuation.currentValue)}
								</p>
								<p className="text-xs text-gray-400 mt-1 font-mono">
									Based on current NOI and market cap rate
								</p>
							</div>
							<div className="bg-gray-800 rounded-lg p-4">
								<h3 className="text-lg font-semibold mb-2 text-gray-200">
									Projected Property Value
								</h3>
								<p className="text-2xl font-bold font-mono text-green-400">
									{new Intl.NumberFormat("en-US", {
										style: "currency",
										currency: "USD",
										minimumFractionDigits: 0,
									}).format(result.propertyValuation.projectedValue)}
								</p>
								<p className="text-xs text-gray-400 mt-1 font-mono">
									After improvements and NOI increase
								</p>
							</div>
						</div>
						<div className="mt-4 bg-gray-800 rounded-lg p-4">
							<h3 className="text-lg font-semibold mb-2 text-gray-200">
								Equity Growth
							</h3>
							<p className="text-2xl font-bold font-mono text-green-400">
								+
								{new Intl.NumberFormat("en-US", {
									style: "currency",
									currency: "USD",
									minimumFractionDigits: 0,
								}).format(result.propertyValuation.equityGrowth)}
							</p>
							<p className="text-xs text-gray-400 mt-1 font-mono">
								Increase in property value from improvements
							</p>
						</div>
					</div>

					{/* Cash Flow Analysis */}
					<div className="bg-gray-900 border border-gray-700 rounded-lg p-6">
						<h2 className="text-xl font-bold mb-4 text-gray-100">
							Cash Flow Analysis
						</h2>
						<div className="grid grid-cols-1 md:grid-cols-3 gap-6">
							<div className="bg-gray-800 rounded-lg p-4">
								<h3 className="text-lg font-semibold mb-2 text-gray-200">
									Monthly Cash Flow Increase
								</h3>
								<p className="text-2xl font-bold font-mono text-green-400">
									+
									{new Intl.NumberFormat("en-US", {
										style: "currency",
										currency: "USD",
										minimumFractionDigits: 0,
									}).format(result.cashFlowAnalysis.monthlyCashFlowIncrease)}
								</p>
								<p className="text-xs text-gray-400 mt-1 font-mono">
									Additional monthly income
								</p>
							</div>
							<div className="bg-gray-800 rounded-lg p-4">
								<h3 className="text-lg font-semibold mb-2 text-gray-200">
									Annual Cash Flow Increase
								</h3>
								<p className="text-2xl font-bold font-mono text-green-400">
									+
									{new Intl.NumberFormat("en-US", {
										style: "currency",
										currency: "USD",
										minimumFractionDigits: 0,
									}).format(result.cashFlowAnalysis.annualCashFlowIncrease)}
								</p>
								<p className="text-xs text-gray-400 mt-1 font-mono">
									Additional yearly income
								</p>
							</div>
							<div className="bg-gray-800 rounded-lg p-4">
								<h3 className="text-lg font-semibold mb-2 text-gray-200">
									10-Year Cash Flow Benefit
								</h3>
								<p className="text-2xl font-bold font-mono text-green-400">
									+
									{new Intl.NumberFormat("en-US", {
										style: "currency",
										currency: "USD",
										minimumFractionDigits: 0,
									}).format(result.cashFlowAnalysis.tenYearCashFlowBenefit)}
								</p>
								<p className="text-xs text-gray-400 mt-1 font-mono">
									Total additional income over 10 years
								</p>
							</div>
						</div>
					</div>

					{/* Reset Button */}
					<div className="text-center">
						<button
							onClick={handleReset}
							className="px-6 py-3 bg-gray-700 hover:bg-gray-600 text-gray-200 font-bold rounded-lg transition-colors duration-200 font-mono"
						>
							Reset Calculator
						</button>
					</div>
				</div>
			)}

			{/* Information Card */}
			<div className="bg-gray-900 border border-gray-700 rounded-lg p-6">
				<h2 className="text-xl font-bold mb-4 text-gray-100">
					About This Calculator
				</h2>
				<p className="text-gray-300 mb-4 font-mono">
					This comparative deal analyzer helps you evaluate whether property
					improvements are financially worthwhile by comparing current vs
					projected performance across multiple metrics.
				</p>
				<div className="space-y-2">
					<p className="text-sm text-gray-400 font-mono">
						• <strong>Property Metrics Comparison:</strong> Side-by-side
						comparison of NOI and cap rates
					</p>
					<p className="text-sm text-gray-400 font-mono">
						• <strong>Investment ROI Analysis:</strong> Calculates annual ROI,
						payback period, and 10-year net present value
					</p>
					<p className="text-sm text-gray-400 font-mono">
						• <strong>Property Valuation Impact:</strong> Shows how improvements
						affect property value and equity growth
					</p>
					<p className="text-sm text-gray-400 font-mono">
						• <strong>Cash Flow Improvement:</strong> Analyzes monthly, annual,
						and long-term cash flow benefits
					</p>
				</div>
			</div>
		</div>
	);
}
