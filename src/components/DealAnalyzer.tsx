import React, { useState } from "react";
import {
	Container,
	Heading,
	Text,
	Card,
	TextField,
	Button,
	Callout,
	Separator,
	Flex,
	Box,
	Grid,
} from "@radix-ui/themes";
import { ThemeProvider } from "./ThemeProvider";

import {
	calculateComparativeAnalysis,
	type ComparativeAnalysisInput,
	type ComparativeAnalysisResult,
	// Legacy imports for backward compatibility
	calculateCapRate,
	calculateImprovementROI,
	calculateRecessionSensitivity,
	type DealAnalysisInput,
	type DealAnalysisResult,
} from "../lib/dealCalculations";
import { useUrlState } from "../hooks/useUrlState";

interface ComparativeFormData {
	// Current Property State
	currentPurchasePrice: string;
	currentNOI: string;

	// Projected Property State
	projectedNOI: string;
	improvementCost: string;
	projectedTimelineMonths: string;
	marketCapRate: string;

	[key: string]: string;
}

export default function DealAnalyzer() {
	const [formData, setFormData] = useUrlState<ComparativeFormData>({
		// Current Property State
		currentPurchasePrice: "",
		currentNOI: "",

		// Projected Property State
		projectedNOI: "",
		improvementCost: "",
		projectedTimelineMonths: "",
		marketCapRate: "",
	});

	const [result, setResult] = useState<ComparativeAnalysisResult | null>(null);
	const [error, setError] = useState<string>("");
	const [isCalculating, setIsCalculating] = useState(false);

	const handleInputChange = (
		field: keyof ComparativeFormData,
		value: string,
	) => {
		// Only allow digits and decimal points
		const rawValue = value.replace(/[^\d.]/g, "");

		if (result) setResult(null);
		if (error) setError("");
		setFormData({ [field]: rawValue });
	};

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();

		setResult(null);
		setError("");
		setIsCalculating(true);

		try {
			// Parse input values
			const input: ComparativeAnalysisInput = {
				current: {
					purchasePrice: parseFloat(formData.currentPurchasePrice) || 0,
					currentNOI: parseFloat(formData.currentNOI) || 0,
					// Set gross income and expenses to NOI for calculation compatibility
					currentGrossIncome: parseFloat(formData.currentNOI) || 0,
					currentExpenses: 0,
				},
				projected: {
					projectedNOI: parseFloat(formData.projectedNOI) || 0,
					// Set gross income and expenses to NOI for calculation compatibility
					projectedGrossIncome: parseFloat(formData.projectedNOI) || 0,
					projectedExpenses: 0,
					improvementCost: parseFloat(formData.improvementCost) || 0,
					projectedTimelineMonths:
						parseFloat(formData.projectedTimelineMonths) || 0,
					marketCapRate: parseFloat(formData.marketCapRate) || 0,
				},
			};

			// Validate inputs
			if (input.current.purchasePrice <= 0) {
				setError("Current purchase price must be greater than $0");
				return;
			}
			if (input.current.currentNOI <= 0) {
				setError("Current NOI must be greater than $0");
				return;
			}
			if (input.projected.projectedNOI <= 0) {
				setError("Projected NOI must be greater than $0");
				return;
			}
			if (input.projected.improvementCost < 0) {
				setError("Improvement cost cannot be negative");
				return;
			}
			if (input.projected.projectedTimelineMonths <= 0) {
				setError("Project timeline must be greater than 0 months");
				return;
			}
			if (input.projected.marketCapRate <= 0) {
				setError("Market cap rate must be greater than 0%");
				return;
			}

			// Simulate brief loading for better UX
			await new Promise((resolve) => setTimeout(resolve, 300));

			// Calculate comparative analysis results
			const analysisResult = calculateComparativeAnalysis(input);
			setResult(analysisResult);
		} catch {
			setError("An unexpected error occurred. Please try again.");
		} finally {
			setIsCalculating(false);
		}
	};

	const handleReset = () => {
		setFormData({
			// Current Property State
			currentPurchasePrice: "",
			currentNOI: "",

			// Projected Property State
			projectedNOI: "",
			improvementCost: "",
			projectedTimelineMonths: "",
			marketCapRate: "",
		});
		setResult(null);
		setError("");
	};

	const isFormValid =
		parseFloat(formData.currentPurchasePrice) > 0 &&
		parseFloat(formData.currentNOI) > 0 &&
		parseFloat(formData.projectedNOI) > 0 &&
		parseFloat(formData.marketCapRate) > 0;

	return (
		<ThemeProvider accentColor="gray" grayColor="gray" radius="medium">
			<Container size="2" p="4">
				<Flex direction="column" gap="6">
					{/* Header */}
					<Box style={{ textAlign: "center" }}>
						<Heading size="8" mb="2">
							Comparative Deal Analyzer
						</Heading>
						<Text color="gray">
							Compare current vs projected property performance to determine if
							improvements are financially worthwhile.
						</Text>
					</Box>

					{/* Calculator Form */}
					<Card>
						<form onSubmit={handleSubmit}>
							<Flex direction="column" gap="4">
								{/* Current Property State */}
								<Box>
									<Heading size="4" mb="3">
										Current Property State
									</Heading>
									<Grid columns={{ initial: "1", sm: "2" }} gap="3">
										<Box>
											<Text as="label" size="2" weight="bold" mb="1">
												Purchase Price
											</Text>
											<TextField.Root
												placeholder="500000"
												value={formData.currentPurchasePrice}
												onChange={(e) =>
													handleInputChange(
														"currentPurchasePrice",
														e.target.value,
													)
												}
											/>
											{formData.currentPurchasePrice && (
												<Text size="1" color="gray">
													{new Intl.NumberFormat("en-US", {
														style: "currency",
														currency: "USD",
														minimumFractionDigits: 0,
														maximumFractionDigits: 0,
													}).format(
														parseFloat(formData.currentPurchasePrice) || 0,
													)}
												</Text>
											)}
										</Box>
										<Box>
											<Text as="label" size="2" weight="bold" mb="1">
												Current NOI
											</Text>
											<TextField.Root
												placeholder="50000"
												value={formData.currentNOI}
												onChange={(e) =>
													handleInputChange("currentNOI", e.target.value)
												}
											/>
											{formData.currentNOI && (
												<Text size="1" color="gray">
													{new Intl.NumberFormat("en-US", {
														style: "currency",
														currency: "USD",
														minimumFractionDigits: 0,
														maximumFractionDigits: 0,
													}).format(parseFloat(formData.currentNOI) || 0)}
												</Text>
											)}
										</Box>
									</Grid>
								</Box>

								<Separator />

								{/* Projected Property State */}
								<Box>
									<Heading size="4" mb="3">
										Projected Property State (After Improvements)
									</Heading>
									{/* Dual Input Design - Exact Match to Screenshot */}
									<Box>
										<Text as="label" size="2" weight="bold" mb="3">
											Investment Analysis
										</Text>

										{/* Connected Input Fields - Seamless Design */}
										<Box
											style={{
												display: "flex",
												border: "2px solid var(--gray-7)",
												borderRadius: "12px",
												overflow: "hidden",
												backgroundColor: "var(--gray-1)",
												marginBottom: "24px",
												boxShadow: "0 1px 3px rgba(0,0,0,0.1)",
											}}
										>
											{/* Left Field - Projected NOI */}
											<Box
												style={{
													flex: 1,
													borderRight: "1px solid var(--gray-7)",
													display: "flex",
													alignItems: "center",
													justifyContent: "center",
													padding: "20px 16px",
													backgroundColor: "var(--gray-1)",
												}}
											>
												<Text
													style={{
														fontSize: "24px",
														fontWeight: "600",
														fontFamily: "var(--font-mono)",
														color: "var(--gray-12)",
														textAlign: "center",
													}}
												>
													{formData.projectedNOI
														? new Intl.NumberFormat("en-US", {
																style: "currency",
																currency: "USD",
																minimumFractionDigits: 0,
															}).format(parseFloat(formData.projectedNOI))
														: "$0"}
												</Text>
											</Box>

											{/* Right Field - ROI Percentage */}
											<Box
												style={{
													flex: 1,
													display: "flex",
													alignItems: "center",
													justifyContent: "center",
													padding: "20px 16px",
													backgroundColor: "var(--gray-1)",
												}}
											>
												<Text
													style={{
														fontSize: "24px",
														fontWeight: "600",
														fontFamily: "var(--font-mono)",
														color: "var(--gray-12)",
														textAlign: "center",
													}}
												>
													{formData.projectedNOI &&
													formData.improvementCost &&
													formData.currentNOI
														? `${(
																((parseFloat(formData.projectedNOI) -
																	parseFloat(formData.currentNOI)) /
																	parseFloat(formData.improvementCost)) *
																100
															).toFixed(1)}%`
														: "0.0%"}
												</Text>
											</Box>
										</Box>

										{/* Interactive Slider */}
										<Box style={{ marginBottom: "16px" }}>
											<input
												type="range"
												min="0"
												max="100"
												step="1"
												value={
													formData.projectedNOI && formData.currentNOI
														? Math.min(
																((parseFloat(formData.projectedNOI) -
																	parseFloat(formData.currentNOI)) /
																	parseFloat(formData.currentNOI)) *
																	100,
																100,
															)
														: 0
												}
												onChange={(e) => {
													const percentage = parseFloat(e.target.value);
													const currentNOI = parseFloat(
														formData.currentNOI || "0",
													);
													const newProjectedNOI =
														currentNOI * (1 + percentage / 100);
													const newImprovementCost =
														(newProjectedNOI - currentNOI) * 5; // Assume 5x multiplier for cost

													setFormData({
														projectedNOI: newProjectedNOI.toFixed(0),
														improvementCost: newImprovementCost.toFixed(0),
													});
												}}
												style={{
													width: "100%",
													height: "8px",
													borderRadius: "4px",
													background: `linear-gradient(to right,
														var(--teal-9) 0%,
														var(--teal-9) ${
															formData.projectedNOI && formData.currentNOI
																? Math.min(
																		((parseFloat(formData.projectedNOI) -
																			parseFloat(formData.currentNOI)) /
																			parseFloat(formData.currentNOI)) *
																			100,
																		100,
																	)
																: 0
														}%,
														var(--gray-6) ${
															formData.projectedNOI && formData.currentNOI
																? Math.min(
																		((parseFloat(formData.projectedNOI) -
																			parseFloat(formData.currentNOI)) /
																			parseFloat(formData.currentNOI)) *
																			100,
																		100,
																	)
																: 0
														}%,
														var(--gray-6) 100%)`,
													outline: "none",
													appearance: "none",
													WebkitAppearance: "none",
													cursor: "pointer",
												}}
											/>
											<style jsx>{`
												input[type="range"]::-webkit-slider-thumb {
													appearance: none;
													width: 20px;
													height: 20px;
													border-radius: 50%;
													background-color: var(--gray-1);
													border: 3px solid var(--teal-9);
													cursor: pointer;
													box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
												}
												input[type="range"]::-moz-range-thumb {
													width: 20px;
													height: 20px;
													border-radius: 50%;
													background-color: var(--gray-1);
													border: 3px solid var(--teal-9);
													cursor: pointer;
													box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
													border: none;
												}
											`}</style>
										</Box>

										{/* Hidden Input Fields for Form Validation */}
										<Box style={{ display: "none" }}>
											<TextField.Root
												value={formData.projectedNOI}
												onChange={(e) =>
													handleInputChange("projectedNOI", e.target.value)
												}
											/>
											<TextField.Root
												value={formData.improvementCost}
												onChange={(e) =>
													handleInputChange("improvementCost", e.target.value)
												}
											/>
										</Box>
									</Box>

									{/* Additional Fields Grid */}
									<Grid columns={{ initial: "1", sm: "2" }} gap="3">
										<Box>
											<Text as="label" size="2" weight="bold" mb="1">
												Timeline (Months)
											</Text>
											<TextField.Root
												placeholder="6"
												value={formData.projectedTimelineMonths}
												onChange={(e) =>
													handleInputChange(
														"projectedTimelineMonths",
														e.target.value,
													)
												}
											/>
											{formData.projectedTimelineMonths && (
												<Text size="1" color="gray">
													{parseFloat(formData.projectedTimelineMonths) || 0}{" "}
													months
												</Text>
											)}
										</Box>
										<Box>
											<Text as="label" size="2" weight="bold" mb="1">
												Market Cap Rate (%)
											</Text>
											<TextField.Root
												placeholder="6.5"
												value={formData.marketCapRate}
												onChange={(e) =>
													handleInputChange("marketCapRate", e.target.value)
												}
											/>
											{formData.marketCapRate && (
												<Text size="1" color="gray">
													{(parseFloat(formData.marketCapRate) || 0).toFixed(2)}
													%
												</Text>
											)}
										</Box>
									</Grid>
								</Box>

								{/* Error Message */}
								{error && (
									<Callout.Root color="red">
										<Callout.Icon>⚠️</Callout.Icon>
										<Callout.Text>{error}</Callout.Text>
									</Callout.Root>
								)}

								{/* Submit Button */}
								<Button
									type="submit"
									disabled={!isFormValid || isCalculating}
									size="3"
								>
									{isCalculating ? "Comparing..." : "Compare Properties"}
								</Button>
							</Flex>
						</form>
					</Card>

					{/* Comparative Analysis Results */}
					{result && (
						<Flex direction="column" gap="4">
							{/* Property Metrics Comparison */}
							<Card>
								<Heading size="4" mb="3">
									Property Metrics Comparison
								</Heading>
								<Box style={{ overflowX: "auto" }}>
									<table
										style={{
											width: "100%",
											borderCollapse: "collapse",
											fontFamily: "var(--font-mono)",
											fontSize: "14px",
										}}
									>
										<thead>
											<tr
												style={{
													backgroundColor: "var(--gray-3)",
													borderBottom: "2px solid var(--gray-6)",
												}}
											>
												<th
													style={{
														padding: "12px 8px",
														textAlign: "left",
														fontWeight: "bold",
														color: "var(--gray-12)",
													}}
												>
													Metric
												</th>
												<th
													style={{
														padding: "12px 8px",
														textAlign: "right",
														fontWeight: "bold",
														color: "var(--gray-12)",
													}}
												>
													Current
												</th>
												<th
													style={{
														padding: "12px 8px",
														textAlign: "right",
														fontWeight: "bold",
														color: "var(--gray-12)",
													}}
												>
													Projected
												</th>
												<th
													style={{
														padding: "12px 8px",
														textAlign: "right",
														fontWeight: "bold",
														color: "var(--gray-12)",
													}}
												>
													Difference
												</th>
												<th
													style={{
														padding: "12px 8px",
														textAlign: "right",
														fontWeight: "bold",
														color: "var(--gray-12)",
													}}
												>
													% Change
												</th>
											</tr>
										</thead>
										<tbody>
											{/* NOI Row */}
											<tr
												style={{
													borderBottom: "1px solid var(--gray-4)",
													backgroundColor: "var(--gray-1)",
												}}
											>
												<td
													style={{
														padding: "12px 8px",
														fontWeight: "500",
														color: "var(--gray-12)",
													}}
												>
													Net Operating Income
												</td>
												<td
													style={{
														padding: "12px 8px",
														textAlign: "right",
														fontWeight: "500",
														color: "var(--gray-11)",
													}}
												>
													{new Intl.NumberFormat("en-US", {
														style: "currency",
														currency: "USD",
														minimumFractionDigits: 0,
													}).format(result.noiComparison.current)}
												</td>
												<td
													style={{
														padding: "12px 8px",
														textAlign: "right",
														fontWeight: "500",
														color: "var(--gray-11)",
													}}
												>
													{new Intl.NumberFormat("en-US", {
														style: "currency",
														currency: "USD",
														minimumFractionDigits: 0,
													}).format(result.noiComparison.projected)}
												</td>
												<td
													style={{
														padding: "12px 8px",
														textAlign: "right",
														fontWeight: "600",
														color:
															result.noiComparison.difference >= 0
																? "var(--green-11)"
																: "var(--red-11)",
													}}
												>
													{result.noiComparison.difference >= 0 ? "+" : ""}
													{new Intl.NumberFormat("en-US", {
														style: "currency",
														currency: "USD",
														minimumFractionDigits: 0,
													}).format(result.noiComparison.difference)}
												</td>
												<td
													style={{
														padding: "12px 8px",
														textAlign: "right",
														fontWeight: "600",
														color:
															result.noiComparison.difference >= 0
																? "var(--green-11)"
																: "var(--red-11)",
													}}
												>
													{result.noiComparison.difference >= 0 ? "+" : ""}
													{result.noiComparison.percentageChange.toFixed(1)}%
												</td>
											</tr>
											{/* Cap Rate Row */}
											<tr
												style={{
													borderBottom: "1px solid var(--gray-4)",
													backgroundColor: "var(--gray-1)",
												}}
											>
												<td
													style={{
														padding: "12px 8px",
														fontWeight: "500",
														color: "var(--gray-12)",
													}}
												>
													Cap Rate
												</td>
												<td
													style={{
														padding: "12px 8px",
														textAlign: "right",
														fontWeight: "500",
														color: "var(--gray-11)",
													}}
												>
													{result.capRateComparison.current.toFixed(2)}%
												</td>
												<td
													style={{
														padding: "12px 8px",
														textAlign: "right",
														fontWeight: "500",
														color: "var(--gray-11)",
													}}
												>
													{result.capRateComparison.projected.toFixed(2)}%
												</td>
												<td
													style={{
														padding: "12px 8px",
														textAlign: "right",
														fontWeight: "600",
														color:
															result.capRateComparison.difference >= 0
																? "var(--green-11)"
																: "var(--red-11)",
													}}
												>
													{result.capRateComparison.difference >= 0 ? "+" : ""}
													{result.capRateComparison.difference.toFixed(2)}%
												</td>
												<td
													style={{
														padding: "12px 8px",
														textAlign: "right",
														fontWeight: "600",
														color:
															result.capRateComparison.difference >= 0
																? "var(--green-11)"
																: "var(--red-11)",
													}}
												>
													{result.capRateComparison.difference >= 0 ? "+" : ""}
													{result.capRateComparison.percentageChange.toFixed(1)}
													%
												</td>
											</tr>
										</tbody>
									</table>
								</Box>
							</Card>

							{/* ROI Analysis */}
							<Card>
								<Heading size="4" mb="3">
									Investment ROI Analysis
								</Heading>
								<Grid columns={{ initial: "1", sm: "2", md: "4" }} gap="4">
									<Box style={{ textAlign: "center" }}>
										<Text size="2" color="gray" weight="bold">
											Total Investment
										</Text>
										<Heading size="5">
											{new Intl.NumberFormat("en-US", {
												style: "currency",
												currency: "USD",
												minimumFractionDigits: 0,
											}).format(result.roiAnalysis.totalInvestment)}
										</Heading>
									</Box>
									<Box style={{ textAlign: "center" }}>
										<Text size="2" color="gray" weight="bold">
											Annual ROI
										</Text>
										<Heading
											size="5"
											color={
												result.roiAnalysis.roi >= 15
													? "green"
													: result.roiAnalysis.roi >= 8
														? "yellow"
														: "red"
											}
										>
											{result.roiAnalysis.roi.toFixed(1)}%
										</Heading>
									</Box>
									<Box style={{ textAlign: "center" }}>
										<Text size="2" color="gray" weight="bold">
											Payback Period
										</Text>
										<Heading size="5">
											{result.roiAnalysis.paybackPeriodYears.toFixed(1)} years
										</Heading>
									</Box>
									<Box style={{ textAlign: "center" }}>
										<Text size="2" color="gray" weight="bold">
											10-Year NPV
										</Text>
										<Heading
											size="5"
											color={
												result.roiAnalysis.netPresentValue >= 0
													? "green"
													: "red"
											}
										>
											{new Intl.NumberFormat("en-US", {
												style: "currency",
												currency: "USD",
												minimumFractionDigits: 0,
											}).format(result.roiAnalysis.netPresentValue)}
										</Heading>
									</Box>
								</Grid>
								<Box p="3" style={{ backgroundColor: "var(--gray-2)" }}>
									<Text>{result.roiAnalysis.analysis}</Text>
								</Box>
							</Card>

							{/* Property Valuation */}
							<Card>
								<Heading size="4" mb="3">
									Property Valuation Impact
								</Heading>
								<Grid columns={{ initial: "1", sm: "2" }} gap="4">
									<Box style={{ textAlign: "center" }}>
										<Text size="2" color="gray" weight="bold">
											Current Value
										</Text>
										<Heading size="6">
											{new Intl.NumberFormat("en-US", {
												style: "currency",
												currency: "USD",
												minimumFractionDigits: 0,
											}).format(result.propertyValuation.currentValue)}
										</Heading>
									</Box>
									<Box style={{ textAlign: "center" }}>
										<Text size="2" color="gray" weight="bold">
											Projected Value
										</Text>
										<Heading size="6">
											{new Intl.NumberFormat("en-US", {
												style: "currency",
												currency: "USD",
												minimumFractionDigits: 0,
											}).format(result.propertyValuation.projectedValue)}
										</Heading>
									</Box>
								</Grid>
								<Box style={{ textAlign: "center" }} mt="3">
									<Text size="2" color="gray" weight="bold">
										Value Increase
									</Text>
									<Heading
										size="5"
										color={
											result.propertyValuation.valueIncrease >= 0
												? "green"
												: "red"
										}
									>
										{result.propertyValuation.valueIncrease >= 0 ? "+" : ""}
										{new Intl.NumberFormat("en-US", {
											style: "currency",
											currency: "USD",
											minimumFractionDigits: 0,
										}).format(result.propertyValuation.valueIncrease)}{" "}
										(
										{result.propertyValuation.valueIncreasePercentage.toFixed(
											1,
										)}
										%)
									</Heading>
								</Box>
								<Box p="3" style={{ backgroundColor: "var(--gray-2)" }}>
									<Text>{result.propertyValuation.analysis}</Text>
								</Box>
							</Card>

							{/* Cash Flow Analysis */}
							<Card>
								<Heading size="4" mb="3">
									Cash Flow Improvement
								</Heading>
								<Grid columns={{ initial: "1", sm: "3" }} gap="4">
									<Box style={{ textAlign: "center" }}>
										<Text size="2" color="gray" weight="bold">
											Monthly Increase
										</Text>
										<Heading
											size="6"
											color={
												result.cashFlowAnalysis.monthlyIncrease >= 0
													? "green"
													: "red"
											}
										>
											{result.cashFlowAnalysis.monthlyIncrease >= 0 ? "+" : ""}
											{new Intl.NumberFormat("en-US", {
												style: "currency",
												currency: "USD",
												minimumFractionDigits: 0,
											}).format(result.cashFlowAnalysis.monthlyIncrease)}
										</Heading>
									</Box>
									<Box style={{ textAlign: "center" }}>
										<Text size="2" color="gray" weight="bold">
											Annual Increase
										</Text>
										<Heading
											size="6"
											color={
												result.cashFlowAnalysis.annualIncrease >= 0
													? "green"
													: "red"
											}
										>
											{result.cashFlowAnalysis.annualIncrease >= 0 ? "+" : ""}
											{new Intl.NumberFormat("en-US", {
												style: "currency",
												currency: "USD",
												minimumFractionDigits: 0,
											}).format(result.cashFlowAnalysis.annualIncrease)}
										</Heading>
									</Box>
									<Box style={{ textAlign: "center" }}>
										<Text size="2" color="gray" weight="bold">
											5-Year Cumulative
										</Text>
										<Heading
											size="6"
											color={
												result.cashFlowAnalysis.cumulativeIncrease5Years >= 0
													? "green"
													: "red"
											}
										>
											{result.cashFlowAnalysis.cumulativeIncrease5Years >= 0
												? "+"
												: ""}
											{new Intl.NumberFormat("en-US", {
												style: "currency",
												currency: "USD",
												minimumFractionDigits: 0,
											}).format(
												result.cashFlowAnalysis.cumulativeIncrease5Years,
											)}
										</Heading>
									</Box>
								</Grid>
								<Box p="3" style={{ backgroundColor: "var(--gray-2)" }}>
									<Text>{result.cashFlowAnalysis.analysis}</Text>
								</Box>
							</Card>

							{/* Reset Button */}
							<Button variant="soft" onClick={handleReset}>
								Compare Another Property
							</Button>
						</Flex>
					)}

					{/* Info Section */}
					<Card>
						<Heading size="4" mb="3">
							About the Comparative Deal Analyzer
						</Heading>
						<Text color="gray">
							This comparative analysis tool helps you determine if property
							improvements are financially worthwhile by comparing current vs
							projected performance:
						</Text>
						<Flex direction="column" gap="2" mt="3">
							<Text size="2">
								• <strong>Property Metrics Comparison:</strong> Side-by-side
								comparison of NOI and cap rates
							</Text>
							<Text size="2">
								• <strong>Investment ROI Analysis:</strong> Calculates annual
								ROI, payback period, and 10-year net present value
							</Text>
							<Text size="2">
								• <strong>Property Valuation Impact:</strong> Shows how
								improvements affect property value and equity growth
							</Text>
							<Text size="2">
								• <strong>Cash Flow Improvement:</strong> Analyzes monthly,
								annual, and long-term cash flow benefits
							</Text>
						</Flex>
					</Card>
				</Flex>
			</Container>
		</ThemeProvider>
	);
}
