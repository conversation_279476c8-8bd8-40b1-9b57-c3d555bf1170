import React, { useState } from "react";
import { z } from "zod";

import {
	calculateComparativeAnalysis,
	type ComparativeAnalysisInput,
	type ComparativeAnalysisResult,
} from "../lib/dealCalculations";
import { useUrlState } from "../hooks/useUrlState";

// Zod schema for form data validation
const ComparativeFormDataSchema = z.object({
	// Current Property State
	currentPurchasePrice: z.string().min(1, "Purchase price is required"),
	currentNOI: z.string().min(1, "Current NOI is required"),

	// Projected Property State
	projectedNOI: z.string().min(1, "Projected NOI is required"),
	improvementCost: z.string().min(1, "Improvement cost is required"),
	projectedTimelineMonths: z.string().min(1, "Timeline is required"),
	marketCapRate: z.string().min(1, "Market cap rate is required"),
});

// Zod schema for parsed numeric values
const ComparativeAnalysisInputSchema = z.object({
	current: z.object({
		purchasePrice: z
			.number()
			.positive("Purchase price must be greater than $0"),
		currentNOI: z.number().positive("Current NOI must be greater than $0"),
		currentGrossIncome: z
			.number()
			.nonnegative("Gross income cannot be negative"),
		currentExpenses: z.number().nonnegative("Expenses cannot be negative"),
	}),
	projected: z.object({
		projectedNOI: z.number().positive("Projected NOI must be greater than $0"),
		projectedGrossIncome: z
			.number()
			.nonnegative("Projected gross income cannot be negative"),
		projectedExpenses: z
			.number()
			.nonnegative("Projected expenses cannot be negative"),
		improvementCost: z
			.number()
			.nonnegative("Improvement cost cannot be negative"),
		projectedTimelineMonths: z
			.number()
			.positive("Timeline must be greater than 0 months"),
		marketCapRate: z
			.number()
			.positive("Market cap rate must be greater than 0%"),
	}),
});

type ComparativeFormData = z.infer<typeof ComparativeFormDataSchema>;

// Helper function to validate individual field values
const validateFieldValue = (
	field: keyof ComparativeFormData,
	value: string,
): string | null => {
	if (!value.trim()) return null; // Don't validate empty fields

	const numericValue = parseFloat(value);
	if (isNaN(numericValue)) return "Must be a valid number";

	switch (field) {
		case "currentPurchasePrice":
		case "currentNOI":
		case "projectedNOI":
		case "projectedTimelineMonths":
		case "marketCapRate":
			return numericValue <= 0 ? "Must be greater than 0" : null;
		case "improvementCost":
			return numericValue < 0 ? "Cannot be negative" : null;
		default:
			return null;
	}
};

export default function DealAnalyzer() {
	const [formData, setFormData] = useUrlState<ComparativeFormData>({
		// Current Property State
		currentPurchasePrice: "",
		currentNOI: "",

		// Projected Property State
		projectedNOI: "",
		improvementCost: "",
		projectedTimelineMonths: "",
		marketCapRate: "",
	});

	const [result, setResult] = useState<ComparativeAnalysisResult | null>(null);
	const [error, setError] = useState<string>("");
	const [isCalculating, setIsCalculating] = useState(false);
	const [fieldErrors, setFieldErrors] = useState<
		Partial<Record<keyof ComparativeFormData, string>>
	>({});

	const handleInputChange = (
		field: keyof ComparativeFormData,
		value: string,
	) => {
		// Only allow digits and decimal points
		const rawValue = value.replace(/[^\d.]/g, "");

		// Validate that it's a valid number format (optional validation)
		if (rawValue && !/^\d*\.?\d*$/.test(rawValue)) {
			return; // Don't update if invalid format
		}

		// Validate field value and update field errors
		const fieldError = validateFieldValue(field, rawValue);
		setFieldErrors((prev) => ({
			...prev,
			[field]: fieldError || undefined,
		}));

		if (result) setResult(null);
		if (error) setError("");
		setFormData({ [field]: rawValue });
	};

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();

		setResult(null);
		setError("");
		setIsCalculating(true);

		try {
			// Validate all fields and collect errors
			const newFieldErrors: Partial<Record<keyof ComparativeFormData, string>> =
				{};

			// Validate each field individually
			Object.keys(formData).forEach((key) => {
				const fieldKey = key as keyof ComparativeFormData;
				const fieldError = validateFieldValue(fieldKey, formData[fieldKey]);
				if (fieldError) {
					newFieldErrors[fieldKey] = fieldError;
				}
			});

			// Also validate required fields
			const formValidation = ComparativeFormDataSchema.safeParse(formData);
			if (!formValidation.success) {
				formValidation.error.errors.forEach((error) => {
					const fieldName = error.path[0] as keyof ComparativeFormData;
					if (!newFieldErrors[fieldName]) {
						newFieldErrors[fieldName] = error.message;
					}
				});
			}

			// Update field errors state
			setFieldErrors(newFieldErrors);

			// If there are any field errors, don't proceed with calculation
			if (Object.keys(newFieldErrors).length > 0) {
				setError("Please fix the errors above before submitting.");
				return;
			}

			// Parse and validate numeric values
			const input: ComparativeAnalysisInput = {
				current: {
					purchasePrice: parseFloat(formData.currentPurchasePrice),
					currentNOI: parseFloat(formData.currentNOI),
					// Set gross income and expenses to NOI for calculation compatibility
					currentGrossIncome: parseFloat(formData.currentNOI),
					currentExpenses: 0,
				},
				projected: {
					projectedNOI: parseFloat(formData.projectedNOI),
					// Set gross income and expenses to NOI for calculation compatibility
					projectedGrossIncome: parseFloat(formData.projectedNOI),
					projectedExpenses: 0,
					improvementCost: parseFloat(formData.improvementCost),
					projectedTimelineMonths: parseFloat(formData.projectedTimelineMonths),
					marketCapRate: parseFloat(formData.marketCapRate),
				},
			};

			// Validate parsed numeric values with Zod
			const inputValidation = ComparativeAnalysisInputSchema.safeParse(input);
			if (!inputValidation.success) {
				const firstError = inputValidation.error.errors[0];
				setError(firstError.message);
				return;
			}

			// Simulate brief loading for better UX
			await new Promise((resolve) => setTimeout(resolve, 300));

			// Calculate comparative analysis results
			const analysisResult = calculateComparativeAnalysis(inputValidation.data);
			setResult(analysisResult);
		} catch {
			setError("An unexpected error occurred. Please try again.");
		} finally {
			setIsCalculating(false);
		}
	};

	const handleReset = () => {
		setFormData({
			// Current Property State
			currentPurchasePrice: "",
			currentNOI: "",

			// Projected Property State
			projectedNOI: "",
			improvementCost: "",
			projectedTimelineMonths: "",
			marketCapRate: "",
		});
		setResult(null);
		setError("");
		setFieldErrors({});
	};

	// Always allow form submission - we'll show validation errors to guide users
	const isFormValid = true;

	return (
		<div className="min-h-screen bg-gradient-to-br from-green-800 via-green-700 to-green-900 py-6 px-4 sm:px-6 lg:px-8">
			<div className="max-w-6xl mx-auto">
				{/* Monopoly-Style Header */}
				<div className="text-center mb-8 relative">
					<div className="bg-red-600 border-4 border-yellow-400 rounded-lg p-6 shadow-2xl transform -rotate-1 hover:rotate-0 transition-transform duration-300">
						<h1 className="text-4xl sm:text-5xl font-bold text-white mb-4 font-serif tracking-wider drop-shadow-lg">
							🏠 PROPERTY DEAL ANALYZER 🏠
						</h1>
						<div className="bg-white border-2 border-black rounded p-3 mx-4">
							<p className="text-lg text-black font-bold font-serif">
								"Compare current vs projected property performance to determine if improvements are financially worthwhile."
							</p>
						</div>
						<div className="absolute -top-2 -right-2 bg-yellow-400 border-2 border-black rounded-full w-12 h-12 flex items-center justify-center">
							<span className="text-2xl">💰</span>
						</div>
					</div>
				</div>

				{/* Monopoly Property Card Form */}
				<div className="bg-white border-8 border-black rounded-lg shadow-2xl overflow-hidden transform hover:scale-105 transition-transform duration-300">
					{/* Property Card Header */}
					<div className="bg-blue-600 border-b-4 border-black p-4 text-center">
						<h2 className="text-2xl font-bold text-white font-serif tracking-wide">
							🏘️ PROPERTY INVESTMENT CALCULATOR 🏘️
						</h2>
					</div>

					<form onSubmit={handleSubmit} className="p-6">
						{/* Current Property State - Monopoly Card Style */}
						<div className="mb-8">
							<div className="bg-gradient-to-r from-orange-400 to-red-500 border-4 border-black rounded-lg p-4 mb-6 shadow-lg">
								<h3 className="text-xl font-bold text-white text-center font-serif tracking-wide drop-shadow-md">
									🏠 CURRENT PROPERTY STATE 🏠
								</h3>
							</div>
							<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
								<div className="bg-gradient-to-b from-green-100 to-green-200 border-4 border-green-800 rounded-lg p-4 shadow-lg">
									<label className="block text-lg font-bold text-green-900 mb-3 font-serif flex items-center">
										💵 Purchase Price
									</label>
									<div className="relative">
										<span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-2xl">$</span>
										<input
											type="text"
											placeholder="500,000"
											value={formData.currentPurchasePrice}
											onChange={(e) =>
												handleInputChange("currentPurchasePrice", e.target.value)
											}
											className={`w-full pl-10 pr-4 py-3 text-xl font-bold border-4 rounded-lg transition-all duration-200 font-mono ${
												fieldErrors.currentPurchasePrice
													? "border-red-600 bg-red-100 text-red-800"
													: "border-green-600 bg-white text-green-900 focus:border-yellow-500 focus:bg-yellow-50"
											}`}
										/>
									</div>
									{fieldErrors.currentPurchasePrice && (
										<div className="mt-2 p-2 bg-red-600 border-2 border-red-800 rounded text-white font-bold text-center">
											⚠️ {fieldErrors.currentPurchasePrice}
										</div>
									)}
									{formData.currentPurchasePrice && !fieldErrors.currentPurchasePrice && (
										<div className="mt-2 p-2 bg-green-600 border-2 border-green-800 rounded text-white font-bold text-center">
											💰 {new Intl.NumberFormat("en-US", {
												style: "currency",
												currency: "USD",
												minimumFractionDigits: 0,
												maximumFractionDigits: 0,
											}).format(parseFloat(formData.currentPurchasePrice) || 0)}
										</div>
									)}
								</div>
								<div className="bg-gradient-to-b from-blue-100 to-blue-200 border-4 border-blue-800 rounded-lg p-4 shadow-lg">
									<label className="block text-lg font-bold text-blue-900 mb-3 font-serif flex items-center">
										🏢 Current NOI (Net Operating Income)
									</label>
									<div className="relative">
										<span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-2xl">$</span>
										<input
											type="text"
											placeholder="50,000"
											value={formData.currentNOI}
											onChange={(e) =>
												handleInputChange("currentNOI", e.target.value)
											}
											className={`w-full pl-10 pr-4 py-3 text-xl font-bold border-4 rounded-lg transition-all duration-200 font-mono ${
												fieldErrors.currentNOI
													? "border-red-600 bg-red-100 text-red-800"
													: "border-blue-600 bg-white text-blue-900 focus:border-yellow-500 focus:bg-yellow-50"
											}`}
										/>
									</div>
									{fieldErrors.currentNOI && (
										<div className="mt-2 p-2 bg-red-600 border-2 border-red-800 rounded text-white font-bold text-center">
											⚠️ {fieldErrors.currentNOI}
										</div>
									)}
									{formData.currentNOI && !fieldErrors.currentNOI && (
										<div className="mt-2 p-2 bg-blue-600 border-2 border-blue-800 rounded text-white font-bold text-center">
											💰 {new Intl.NumberFormat("en-US", {
												style: "currency",
												currency: "USD",
												minimumFractionDigits: 0,
												maximumFractionDigits: 0,
											}).format(parseFloat(formData.currentNOI) || 0)} /year
										</div>
									)}
								</div>
							</div>
						</div>

						{/* Monopoly-Style Section Divider */}
						<div className="my-8 flex items-center">
							<div className="flex-1 border-t-4 border-dashed border-yellow-600"></div>
							<div className="mx-4 bg-yellow-400 border-4 border-black rounded-full p-3 shadow-lg">
								<span className="text-2xl">🔄</span>
							</div>
							<div className="flex-1 border-t-4 border-dashed border-yellow-600"></div>
						</div>

						{/* Projected Property State - Monopoly Card Style */}
						<div className="mb-8">
							<div className="bg-gradient-to-r from-purple-500 to-pink-500 border-4 border-black rounded-lg p-4 mb-6 shadow-lg">
								<h3 className="text-xl font-bold text-white text-center font-serif tracking-wide drop-shadow-md">
									🏗️ PROJECTED PROPERTY STATE (AFTER IMPROVEMENTS) 🏗️
								</h3>
							</div>
							<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
								<div className="bg-gradient-to-b from-purple-100 to-purple-200 border-4 border-purple-800 rounded-lg p-4 shadow-lg">
									<label className="block text-lg font-bold text-purple-900 mb-3 font-serif flex items-center">
										📈 Projected NOI (After Improvements)
									</label>
									<div className="relative">
										<span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-2xl">$</span>
										<input
											type="text"
											placeholder="65,000"
											value={formData.projectedNOI}
											onChange={(e) =>
												handleInputChange("projectedNOI", e.target.value)
											}
											className={`w-full pl-10 pr-4 py-3 text-xl font-bold border-4 rounded-lg transition-all duration-200 font-mono ${
												fieldErrors.projectedNOI
													? "border-red-600 bg-red-100 text-red-800"
													: "border-purple-600 bg-white text-purple-900 focus:border-yellow-500 focus:bg-yellow-50"
											}`}
										/>
									</div>
									{fieldErrors.projectedNOI && (
										<div className="mt-2 p-2 bg-red-600 border-2 border-red-800 rounded text-white font-bold text-center">
											⚠️ {fieldErrors.projectedNOI}
										</div>
									)}
									{formData.projectedNOI && !fieldErrors.projectedNOI && (
										<div className="mt-2 p-2 bg-purple-600 border-2 border-purple-800 rounded text-white font-bold text-center">
											💰 {new Intl.NumberFormat("en-US", {
												style: "currency",
												currency: "USD",
												minimumFractionDigits: 0,
												maximumFractionDigits: 0,
											}).format(parseFloat(formData.projectedNOI) || 0)} /year
										</div>
									)}
								</div>
								<div className="bg-gradient-to-b from-orange-100 to-orange-200 border-4 border-orange-800 rounded-lg p-4 shadow-lg">
									<label className="block text-lg font-bold text-orange-900 mb-3 font-serif flex items-center">
										🔨 Improvement Cost
									</label>
									<div className="relative">
										<span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-2xl">$</span>
										<input
											type="text"
											placeholder="25,000"
											value={formData.improvementCost}
											onChange={(e) =>
												handleInputChange("improvementCost", e.target.value)
											}
											className={`w-full pl-10 pr-4 py-3 text-xl font-bold border-4 rounded-lg transition-all duration-200 font-mono ${
												fieldErrors.improvementCost
													? "border-red-600 bg-red-100 text-red-800"
													: "border-orange-600 bg-white text-orange-900 focus:border-yellow-500 focus:bg-yellow-50"
											}`}
										/>
									</div>
									{fieldErrors.improvementCost && (
										<div className="mt-2 p-2 bg-red-600 border-2 border-red-800 rounded text-white font-bold text-center">
											⚠️ {fieldErrors.improvementCost}
										</div>
									)}
									{formData.improvementCost && !fieldErrors.improvementCost && (
										<div className="mt-2 p-2 bg-orange-600 border-2 border-orange-800 rounded text-white font-bold text-center">
											💰 {new Intl.NumberFormat("en-US", {
												style: "currency",
												currency: "USD",
												minimumFractionDigits: 0,
												maximumFractionDigits: 0,
											}).format(parseFloat(formData.improvementCost) || 0)}
										</div>
									)}
								</div>
							<div className="bg-gradient-to-b from-yellow-100 to-yellow-200 border-4 border-yellow-800 rounded-lg p-4 shadow-lg">
								<label className="block text-lg font-bold text-yellow-900 mb-3 font-serif flex items-center">
									⏰ Timeline (Months)
								</label>
								<div className="relative">
									<span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-2xl">📅</span>
									<input
										type="text"
										placeholder="6"
										value={formData.projectedTimelineMonths}
										onChange={(e) =>
											handleInputChange("projectedTimelineMonths", e.target.value)
										}
										className={`w-full pl-12 pr-4 py-3 text-xl font-bold border-4 rounded-lg transition-all duration-200 font-mono ${
											fieldErrors.projectedTimelineMonths
												? "border-red-600 bg-red-100 text-red-800"
												: "border-yellow-600 bg-white text-yellow-900 focus:border-yellow-500 focus:bg-yellow-50"
										}`}
									/>
								</div>
								{fieldErrors.projectedTimelineMonths && (
									<div className="mt-2 p-2 bg-red-600 border-2 border-red-800 rounded text-white font-bold text-center">
										⚠️ {fieldErrors.projectedTimelineMonths}
									</div>
								)}
								{formData.projectedTimelineMonths && !fieldErrors.projectedTimelineMonths && (
									<div className="mt-2 p-2 bg-yellow-600 border-2 border-yellow-800 rounded text-white font-bold text-center">
										⏱️ {parseFloat(formData.projectedTimelineMonths) || 0} months
									</div>
								)}
							</div>
							<div className="bg-gradient-to-b from-red-100 to-red-200 border-4 border-red-800 rounded-lg p-4 shadow-lg">
								<label className="block text-lg font-bold text-red-900 mb-3 font-serif flex items-center">
									📊 Market Cap Rate (%)
								</label>
								<div className="relative">
									<span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-2xl">%</span>
									<input
										type="text"
										placeholder="6.5"
										value={formData.marketCapRate}
										onChange={(e) =>
											handleInputChange("marketCapRate", e.target.value)
										}
										className={`w-full pl-10 pr-4 py-3 text-xl font-bold border-4 rounded-lg transition-all duration-200 font-mono ${
											fieldErrors.marketCapRate
												? "border-red-600 bg-red-100 text-red-800"
												: "border-red-600 bg-white text-red-900 focus:border-yellow-500 focus:bg-yellow-50"
										}`}
									/>
								</div>
								{fieldErrors.marketCapRate && (
									<div className="mt-2 p-2 bg-red-600 border-2 border-red-800 rounded text-white font-bold text-center">
										⚠️ {fieldErrors.marketCapRate}
									</div>
								)}
								{formData.marketCapRate && !fieldErrors.marketCapRate && (
									<div className="mt-2 p-2 bg-red-600 border-2 border-red-800 rounded text-white font-bold text-center">
										📈 {(parseFloat(formData.marketCapRate) || 0).toFixed(2)}%
									</div>
								)}
							</div>
						</div>
					</div>

						{/* Error Message - Monopoly Style */}
						{error && (
							<div className="mb-6 bg-red-600 border-4 border-black rounded-lg p-4 shadow-lg transform -rotate-1">
								<div className="bg-white border-2 border-black rounded p-3 text-center">
									<p className="text-red-800 font-bold text-lg font-serif flex items-center justify-center">
										🚨 {error} 🚨
									</p>
								</div>
							</div>
						)}

						{/* Monopoly-Style Action Buttons */}
						<div className="flex flex-col sm:flex-row gap-4 pt-6">
							<button
								type="submit"
								disabled={isCalculating}
								className="flex-1 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 disabled:from-gray-400 disabled:to-gray-500 text-white font-bold py-4 px-8 border-4 border-black rounded-lg shadow-lg transform hover:scale-105 transition-all duration-200 disabled:cursor-not-allowed disabled:transform-none font-serif text-xl"
							>
								{isCalculating ? (
									<span className="flex items-center justify-center">
										<span className="animate-spin mr-3 text-2xl">🎲</span>
										CALCULATING DEAL...
									</span>
								) : (
									<span className="flex items-center justify-center">
										🏦 ANALYZE PROPERTY DEAL 🏦
									</span>
								)}
							</button>
							<button
								type="button"
								onClick={handleReset}
								className="sm:w-auto bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white font-bold py-4 px-8 border-4 border-black rounded-lg shadow-lg transform hover:scale-105 transition-all duration-200 font-serif text-xl"
							>
								🔄 RESET
							</button>
						</div>
				</form>
			</div>

				{/* Monopoly-Style Results Board */}
				{result && (
					<div className="mt-8 space-y-8">
						{/* Property Metrics Comparison - Monopoly Board Style */}
						<div className="bg-white border-8 border-black rounded-lg shadow-2xl overflow-hidden">
							<div className="bg-gradient-to-r from-green-600 to-green-700 border-b-4 border-black p-4">
								<h2 className="text-2xl font-bold text-white text-center font-serif tracking-wide drop-shadow-lg">
									🏘️ PROPERTY METRICS COMPARISON BOARD 🏘️
								</h2>
							</div>
							<div className="p-6 bg-gradient-to-br from-green-50 to-green-100">
								<div className="overflow-x-auto">
									<table className="w-full border-4 border-black rounded-lg overflow-hidden shadow-lg"
										<thead>
											<tr className="bg-yellow-400 border-b-4 border-black">
												<th className="py-4 px-6 text-left font-bold text-black font-serif text-lg border-r-2 border-black">
													📊 Metric
												</th>
												<th className="py-4 px-6 text-center font-bold text-black font-serif text-lg border-r-2 border-black">
													🏠 Current
												</th>
												<th className="py-4 px-6 text-center font-bold text-black font-serif text-lg border-r-2 border-black">
													🏗️ Projected
												</th>
												<th className="py-4 px-6 text-center font-bold text-black font-serif text-lg border-r-2 border-black">
													📈 Difference
												</th>
												<th className="py-4 px-6 text-center font-bold text-black font-serif text-lg">
													📊 % Change
												</th>
											</tr>
										</thead>
										<tbody>
											{/* NOI Row - Blue Property Style */}
											<tr className="bg-blue-100 border-b-2 border-black hover:bg-blue-200 transition-colors">
												<td className="py-4 px-6 font-bold text-blue-900 font-serif border-r-2 border-black">
													💰 Net Operating Income
												</td>
												<td className="py-4 px-6 text-center font-bold text-blue-900 font-mono border-r-2 border-black">
													{new Intl.NumberFormat("en-US", {
														style: "currency",
														currency: "USD",
														minimumFractionDigits: 0,
													}).format(result.noiComparison.current)}
												</td>
												<td className="py-4 px-6 text-center font-bold text-blue-900 font-mono border-r-2 border-black">
													{new Intl.NumberFormat("en-US", {
														style: "currency",
														currency: "USD",
														minimumFractionDigits: 0,
													}).format(result.noiComparison.projected)}
												</td>
												<td className={`py-4 px-6 text-center font-bold font-mono border-r-2 border-black ${
													result.noiComparison.difference >= 0
														? "text-green-700 bg-green-100"
														: "text-red-700 bg-red-100"
												}`}>
													{result.noiComparison.difference >= 0 ? "+" : ""}
													{new Intl.NumberFormat("en-US", {
														style: "currency",
														currency: "USD",
														minimumFractionDigits: 0,
													}).format(result.noiComparison.difference)}
												</td>
												<td className={`py-4 px-6 text-center font-bold font-mono ${
													result.noiComparison.percentageChange >= 0
														? "text-green-700 bg-green-100"
														: "text-red-700 bg-red-100"
												}`}>
													{result.noiComparison.percentageChange >= 0 ? "+" : ""}
													{result.noiComparison.percentageChange.toFixed(1)}%
												</td>
											</tr>

											{/* Cap Rate Row - Orange Property Style */}
											<tr className="bg-orange-100 border-b-2 border-black hover:bg-orange-200 transition-colors">
												<td className="py-4 px-6 font-bold text-orange-900 font-serif border-r-2 border-black">
													📊 Cap Rate
												</td>
												<td className="py-4 px-6 text-center font-bold text-orange-900 font-mono border-r-2 border-black">
													{result.capRateComparison.current.toFixed(2)}%
												</td>
												<td className="py-4 px-6 text-center font-bold text-orange-900 font-mono border-r-2 border-black">
													{result.capRateComparison.projected.toFixed(2)}%
												</td>
												<td className={`py-4 px-6 text-center font-bold font-mono border-r-2 border-black ${
													result.capRateComparison.difference >= 0
														? "text-green-700 bg-green-100"
														: "text-red-700 bg-red-100"
												}`}>
													{result.capRateComparison.difference >= 0 ? "+" : ""}
													{result.capRateComparison.difference.toFixed(2)}%
												</td>
												<td className={`py-4 px-6 text-center font-bold font-mono ${
													result.capRateComparison.percentageChange >= 0
														? "text-green-700 bg-green-100"
														: "text-red-700 bg-red-100"
												}`}>
													{result.capRateComparison.percentageChange >= 0 ? "+" : ""}
													{result.capRateComparison.percentageChange.toFixed(1)}%
												</td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>

						{/* Investment ROI Analysis - Monopoly Property Cards */}
						<div className="bg-white border-8 border-black rounded-lg shadow-2xl overflow-hidden">
							<div className="bg-gradient-to-r from-purple-600 to-purple-700 border-b-4 border-black p-4">
								<h2 className="text-2xl font-bold text-white text-center font-serif tracking-wide drop-shadow-lg">
									💎 INVESTMENT ROI ANALYSIS 💎
								</h2>
							</div>
							<div className="p-6 bg-gradient-to-br from-purple-50 to-purple-100">
								<div className="grid grid-cols-1 md:grid-cols-3 gap-6">
									{/* Annual ROI Card */}
									<div className="bg-gradient-to-b from-green-400 to-green-500 border-4 border-black rounded-lg p-6 shadow-lg transform hover:scale-105 transition-transform">
										<div className="bg-white border-2 border-black rounded p-4 text-center">
											<h3 className="text-lg font-bold text-black font-serif mb-2">
												📈 ANNUAL ROI
											</h3>
											<p className={`text-4xl font-bold font-mono mb-2 ${
												result.roiAnalysis.roi >= 15
													? "text-green-700"
													: result.roiAnalysis.roi >= 8
														? "text-yellow-700"
														: "text-red-700"
											}`}>
												{result.roiAnalysis.roi.toFixed(1)}%
											</p>
											<p className="text-sm text-black font-bold">
												Target: 15%+ Excellent, 8%+ Good
											</p>
										</div>
									</div>

									{/* Payback Period Card */}
									<div className="bg-gradient-to-b from-blue-400 to-blue-500 border-4 border-black rounded-lg p-6 shadow-lg transform hover:scale-105 transition-transform">
										<div className="bg-white border-2 border-black rounded p-4 text-center">
											<h3 className="text-lg font-bold text-black font-serif mb-2">
												⏰ PAYBACK PERIOD
											</h3>
											<p className="text-4xl font-bold text-blue-700 font-mono mb-2">
												{result.roiAnalysis.paybackPeriodYears.toFixed(1)} years
											</p>
											<p className="text-sm text-black font-bold">
												Time to recover investment
											</p>
										</div>
									</div>

									{/* 10-Year NPV Card */}
									<div className="bg-gradient-to-b from-red-400 to-red-500 border-4 border-black rounded-lg p-6 shadow-lg transform hover:scale-105 transition-transform">
										<div className="bg-white border-2 border-black rounded p-4 text-center">
											<h3 className="text-lg font-bold text-black font-serif mb-2">
												💰 10-YEAR NPV
											</h3>
											<p className={`text-3xl font-bold font-mono mb-2 ${
												result.roiAnalysis.netPresentValue >= 0
													? "text-green-700"
													: "text-red-700"
											}`}>
												{new Intl.NumberFormat("en-US", {
													style: "currency",
													currency: "USD",
													minimumFractionDigits: 0,
													maximumFractionDigits: 0,
												}).format(result.roiAnalysis.netPresentValue)}
											</p>
											<p className="text-sm text-black font-bold">
												Net present value over 10 years
											</p>
										</div>
									</div>
								</div>
							</div>
						</div>

					{/* Property Valuation Impact */}
					<div>
						<h2>Property Valuation Impact</h2>
						<div>
							<div>
								<h3>Current Property Value</h3>
								<p>
									{new Intl.NumberFormat("en-US", {
										style: "currency",
										currency: "USD",
										minimumFractionDigits: 0,
									}).format(result.propertyValuation.currentValue)}
								</p>
								<p>Based on current NOI and market cap rate</p>
							</div>
							<div>
								<h3>Projected Property Value</h3>
								<p>
									{new Intl.NumberFormat("en-US", {
										style: "currency",
										currency: "USD",
										minimumFractionDigits: 0,
									}).format(result.propertyValuation.projectedValue)}
								</p>
								<p>After improvements and NOI increase</p>
							</div>
						</div>
						<div>
							<h3>Equity Growth</h3>
							<p>
								+
								{new Intl.NumberFormat("en-US", {
									style: "currency",
									currency: "USD",
									minimumFractionDigits: 0,
								}).format(result.propertyValuation.valueIncrease)}
							</p>
							<p>Increase in property value from improvements</p>
						</div>
					</div>

					{/* Cash Flow Analysis */}
					<div>
						<h2>Cash Flow Analysis</h2>
						<div>
							<div>
								<h3>Monthly Cash Flow Increase</h3>
								<p>
									+
									{new Intl.NumberFormat("en-US", {
										style: "currency",
										currency: "USD",
										minimumFractionDigits: 0,
									}).format(result.cashFlowAnalysis.monthlyIncrease)}
								</p>
								<p>Additional monthly income</p>
							</div>
							<div>
								<h3>Annual Cash Flow Increase</h3>
								<p>
									+
									{new Intl.NumberFormat("en-US", {
										style: "currency",
										currency: "USD",
										minimumFractionDigits: 0,
									}).format(result.cashFlowAnalysis.annualIncrease)}
								</p>
								<p>Additional yearly income</p>
							</div>
							<div>
								<h3>5-Year Cash Flow Benefit</h3>
								<p>
									+
									{new Intl.NumberFormat("en-US", {
										style: "currency",
										currency: "USD",
										minimumFractionDigits: 0,
									}).format(result.cashFlowAnalysis.cumulativeIncrease5Years)}
								</p>
								<p>Total additional income over 5 years</p>
							</div>
						</div>
					</div>

					{/* Reset Button */}
					<div>
						<button onClick={handleReset}>Reset Calculator</button>
					</div>
				</div>
			)}

			{/* Information Card */}
			<div>
				<h2>About This Calculator</h2>
				<p>
					This comparative deal analyzer helps you evaluate whether property
					improvements are financially worthwhile by comparing current vs
					projected performance across multiple metrics.
				</p>
				<div>
					<p>
						• <strong>Property Metrics Comparison:</strong> Side-by-side
						comparison of NOI and cap rates
					</p>
					<p>
						• <strong>Investment ROI Analysis:</strong> Calculates annual ROI,
						payback period, and 10-year net present value
					</p>
					<p>
						• <strong>Property Valuation Impact:</strong> Shows how improvements
						affect property value and equity growth
					</p>
					<p>
						• <strong>Cash Flow Improvement:</strong> Analyzes monthly, annual,
						and long-term cash flow benefits
					</p>
				</div>
			</div>
		</div>
	);
}
