import React, { useState } from "react";
import { z } from "zod";

import {
	calculateComparativeAnalysis,
	type ComparativeAnalysisInput,
	type ComparativeAnalysisResult,
} from "../lib/dealCalculations";
import { useUrlState } from "../hooks/useUrlState";

// Zod schema for form data validation
const ComparativeFormDataSchema = z.object({
	// Current Property State
	currentPurchasePrice: z.string().min(1, "Purchase price is required"),
	currentNOI: z.string().min(1, "Current NOI is required"),

	// Projected Property State
	projectedNOI: z.string().min(1, "Projected NOI is required"),
	improvementCost: z.string().min(1, "Improvement cost is required"),
	projectedTimelineMonths: z.string().min(1, "Timeline is required"),
	marketCapRate: z.string().min(1, "Market cap rate is required"),
});

// Zod schema for parsed numeric values
const ComparativeAnalysisInputSchema = z.object({
	current: z.object({
		purchasePrice: z
			.number()
			.positive("Purchase price must be greater than $0"),
		currentNOI: z.number().positive("Current NOI must be greater than $0"),
		currentGrossIncome: z
			.number()
			.nonnegative("Gross income cannot be negative"),
		currentExpenses: z.number().nonnegative("Expenses cannot be negative"),
	}),
	projected: z.object({
		projectedNOI: z.number().positive("Projected NOI must be greater than $0"),
		projectedGrossIncome: z
			.number()
			.nonnegative("Projected gross income cannot be negative"),
		projectedExpenses: z
			.number()
			.nonnegative("Projected expenses cannot be negative"),
		improvementCost: z
			.number()
			.nonnegative("Improvement cost cannot be negative"),
		projectedTimelineMonths: z
			.number()
			.positive("Timeline must be greater than 0 months"),
		marketCapRate: z
			.number()
			.positive("Market cap rate must be greater than 0%"),
	}),
});

type ComparativeFormData = z.infer<typeof ComparativeFormDataSchema>;

// Helper function to validate individual field values
const validateFieldValue = (
	field: keyof ComparativeFormData,
	value: string,
): string | null => {
	if (!value.trim()) return null; // Don't validate empty fields

	const numericValue = parseFloat(value);
	if (isNaN(numericValue)) return "Must be a valid number";

	switch (field) {
		case "currentPurchasePrice":
		case "currentNOI":
		case "projectedNOI":
		case "projectedTimelineMonths":
		case "marketCapRate":
			return numericValue <= 0 ? "Must be greater than 0" : null;
		case "improvementCost":
			return numericValue < 0 ? "Cannot be negative" : null;
		default:
			return null;
	}
};

export default function DealAnalyzer() {
	const [formData, setFormData] = useUrlState<ComparativeFormData>({
		// Current Property State
		currentPurchasePrice: "",
		currentNOI: "",

		// Projected Property State
		projectedNOI: "",
		improvementCost: "",
		projectedTimelineMonths: "",
		marketCapRate: "",
	});

	const [result, setResult] = useState<ComparativeAnalysisResult | null>(null);
	const [error, setError] = useState<string>("");
	const [isCalculating, setIsCalculating] = useState(false);
	const [fieldErrors, setFieldErrors] = useState<
		Partial<Record<keyof ComparativeFormData, string>>
	>({});

	const handleInputChange = (
		field: keyof ComparativeFormData,
		value: string,
	) => {
		// Only allow digits and decimal points
		const rawValue = value.replace(/[^\d.]/g, "");

		// Validate that it's a valid number format (optional validation)
		if (rawValue && !/^\d*\.?\d*$/.test(rawValue)) {
			return; // Don't update if invalid format
		}

		// Validate field value and update field errors
		const fieldError = validateFieldValue(field, rawValue);
		setFieldErrors((prev) => ({
			...prev,
			[field]: fieldError || undefined,
		}));

		if (result) setResult(null);
		if (error) setError("");
		setFormData({ [field]: rawValue });
	};

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();

		setResult(null);
		setError("");
		setIsCalculating(true);

		try {
			// Validate all fields and collect errors
			const newFieldErrors: Partial<Record<keyof ComparativeFormData, string>> =
				{};

			// Validate each field individually
			Object.keys(formData).forEach((key) => {
				const fieldKey = key as keyof ComparativeFormData;
				const fieldError = validateFieldValue(fieldKey, formData[fieldKey]);
				if (fieldError) {
					newFieldErrors[fieldKey] = fieldError;
				}
			});

			// Also validate required fields
			const formValidation = ComparativeFormDataSchema.safeParse(formData);
			if (!formValidation.success) {
				formValidation.error.errors.forEach((error) => {
					const fieldName = error.path[0] as keyof ComparativeFormData;
					if (!newFieldErrors[fieldName]) {
						newFieldErrors[fieldName] = error.message;
					}
				});
			}

			// Update field errors state
			setFieldErrors(newFieldErrors);

			// If there are any field errors, don't proceed with calculation
			if (Object.keys(newFieldErrors).length > 0) {
				setError("Please fix the errors above before submitting.");
				return;
			}

			// Parse and validate numeric values
			const input: ComparativeAnalysisInput = {
				current: {
					purchasePrice: parseFloat(formData.currentPurchasePrice),
					currentNOI: parseFloat(formData.currentNOI),
					// Set gross income and expenses to NOI for calculation compatibility
					currentGrossIncome: parseFloat(formData.currentNOI),
					currentExpenses: 0,
				},
				projected: {
					projectedNOI: parseFloat(formData.projectedNOI),
					// Set gross income and expenses to NOI for calculation compatibility
					projectedGrossIncome: parseFloat(formData.projectedNOI),
					projectedExpenses: 0,
					improvementCost: parseFloat(formData.improvementCost),
					projectedTimelineMonths: parseFloat(formData.projectedTimelineMonths),
					marketCapRate: parseFloat(formData.marketCapRate),
				},
			};

			// Validate parsed numeric values with Zod
			const inputValidation = ComparativeAnalysisInputSchema.safeParse(input);
			if (!inputValidation.success) {
				const firstError = inputValidation.error.errors[0];
				setError(firstError.message);
				return;
			}

			// Simulate brief loading for better UX
			await new Promise((resolve) => setTimeout(resolve, 300));

			// Calculate comparative analysis results
			const analysisResult = calculateComparativeAnalysis(inputValidation.data);
			setResult(analysisResult);
		} catch {
			setError("An unexpected error occurred. Please try again.");
		} finally {
			setIsCalculating(false);
		}
	};

	const handleReset = () => {
		setFormData({
			// Current Property State
			currentPurchasePrice: "",
			currentNOI: "",

			// Projected Property State
			projectedNOI: "",
			improvementCost: "",
			projectedTimelineMonths: "",
			marketCapRate: "",
		});
		setResult(null);
		setError("");
		setFieldErrors({});
	};

	// Always allow form submission - we'll show validation errors to guide users
	const isFormValid = true;

	return (
		<div className="min-h-screen bg-gray-50 py-4 px-4 sm:px-6 lg:px-8">
			<div className="max-w-4xl mx-auto">
				{/* Header */}
				<div className="text-center mb-8">
					<h1 className="text-3xl sm:text-4xl font-light text-gray-900 mb-4">
						Comparative Deal Analyzer
					</h1>
					<p className="text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed">
						Compare current vs projected property performance to determine if
						improvements are financially worthwhile.
					</p>
				</div>

				{/* Calculator Form */}
				<div className="bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden">
					<form onSubmit={handleSubmit} className="p-6 sm:p-8">
						{/* Current Property State */}
						<div className="mb-8">
							<h2 className="text-xl font-light text-gray-900 mb-6 pb-3 border-b border-gray-100">
								Current Property State
							</h2>
							<div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
							<div className="space-y-2">
								<label className="block text-sm font-medium text-gray-700 mb-2">
									Purchase Price
								</label>
								<input
									type="text"
									placeholder="500,000"
									value={formData.currentPurchasePrice}
									onChange={(e) =>
										handleInputChange("currentPurchasePrice", e.target.value)
									}
									className={`w-full px-4 py-3 text-lg border rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent ${
										fieldErrors.currentPurchasePrice
											? "border-red-300 bg-red-50"
											: "border-gray-200 bg-white hover:border-gray-300"
									}`}
								/>
								{fieldErrors.currentPurchasePrice && (
									<p className="text-sm text-red-600 mt-1 flex items-center">
										<span className="mr-1">⚠️</span>
										{fieldErrors.currentPurchasePrice}
									</p>
								)}
								{formData.currentPurchasePrice &&
									!fieldErrors.currentPurchasePrice && (
										<p className="text-sm text-gray-500 mt-1">
											{new Intl.NumberFormat("en-US", {
												style: "currency",
												currency: "USD",
												minimumFractionDigits: 0,
												maximumFractionDigits: 0,
											}).format(parseFloat(formData.currentPurchasePrice) || 0)}
										</p>
									)}
							</div>
							<div className="space-y-2">
								<label className="block text-sm font-medium text-gray-700 mb-2">
									Current NOI
								</label>
								<input
									type="text"
									placeholder="50,000"
									value={formData.currentNOI}
									onChange={(e) =>
										handleInputChange("currentNOI", e.target.value)
									}
									className={`w-full px-4 py-3 text-lg border rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent ${
										fieldErrors.currentNOI
											? "border-red-300 bg-red-50"
											: "border-gray-200 bg-white hover:border-gray-300"
									}`}
								/>
								{fieldErrors.currentNOI && (
									<p className="text-sm text-red-600 mt-1 flex items-center">
										<span className="mr-1">⚠️</span>
										{fieldErrors.currentNOI}
									</p>
								)}
								{formData.currentNOI && !fieldErrors.currentNOI && (
									<p className="text-sm text-gray-500 mt-1">
										{new Intl.NumberFormat("en-US", {
											style: "currency",
											currency: "USD",
											minimumFractionDigits: 0,
											maximumFractionDigits: 0,
										}).format(parseFloat(formData.currentNOI) || 0)}
									</p>
								)}
							</div>
							</div>
						</div>

						{/* Section Divider */}
						<div className="my-8">
							<div className="relative">
								<div className="absolute inset-0 flex items-center">
									<div className="w-full border-t border-gray-200"></div>
								</div>
								<div className="relative flex justify-center">
									<span className="bg-white px-4 text-sm text-gray-500">
										After Improvements
									</span>
								</div>
							</div>
						</div>

						{/* Projected Property State */}
						<div className="mb-8">
							<h2 className="text-xl font-light text-gray-900 mb-6 pb-3 border-b border-gray-100">
								Projected Property State
							</h2>
							<div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
							<div className="space-y-2">
								<label className="block text-sm font-medium text-gray-700 mb-2">
									Projected NOI
								</label>
								<input
									type="text"
									placeholder="65,000"
									value={formData.projectedNOI}
									onChange={(e) =>
										handleInputChange("projectedNOI", e.target.value)
									}
									className={`w-full px-4 py-3 text-lg border rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent ${
										fieldErrors.projectedNOI
											? "border-red-300 bg-red-50"
											: "border-gray-200 bg-white hover:border-gray-300"
									}`}
								/>
								{fieldErrors.projectedNOI && (
									<p className="text-sm text-red-600 mt-1 flex items-center">
										<span className="mr-1">⚠️</span>
										{fieldErrors.projectedNOI}
									</p>
								)}
								{formData.projectedNOI && !fieldErrors.projectedNOI && (
									<p className="text-sm text-gray-500 mt-1">
										{new Intl.NumberFormat("en-US", {
											style: "currency",
											currency: "USD",
											minimumFractionDigits: 0,
											maximumFractionDigits: 0,
										}).format(parseFloat(formData.projectedNOI) || 0)}
									</p>
								)}
							</div>
							<div className="space-y-2">
								<label className="block text-sm font-medium text-gray-700 mb-2">
									Improvement Cost
								</label>
								<input
									type="text"
									placeholder="25,000"
									value={formData.improvementCost}
									onChange={(e) =>
										handleInputChange("improvementCost", e.target.value)
									}
									className={`w-full px-4 py-3 text-lg border rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent ${
										fieldErrors.improvementCost
											? "border-red-300 bg-red-50"
											: "border-gray-200 bg-white hover:border-gray-300"
									}`}
								/>
								{fieldErrors.improvementCost && (
									<p className="text-sm text-red-600 mt-1 flex items-center">
										<span className="mr-1">⚠️</span>
										{fieldErrors.improvementCost}
									</p>
								)}
								{formData.improvementCost && !fieldErrors.improvementCost && (
									<p className="text-sm text-gray-500 mt-1">
										{new Intl.NumberFormat("en-US", {
											style: "currency",
											currency: "USD",
											minimumFractionDigits: 0,
											maximumFractionDigits: 0,
										}).format(parseFloat(formData.improvementCost) || 0)}
									</p>
								)}
							</div>
							<div className="space-y-2">
								<label className="block text-sm font-medium text-gray-700 mb-2">
									Timeline (Months)
								</label>
								<input
									type="text"
									placeholder="6"
									value={formData.projectedTimelineMonths}
									onChange={(e) =>
										handleInputChange("projectedTimelineMonths", e.target.value)
									}
									className={`w-full px-4 py-3 text-lg border rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent ${
										fieldErrors.projectedTimelineMonths
											? "border-red-300 bg-red-50"
											: "border-gray-200 bg-white hover:border-gray-300"
									}`}
								/>
								{fieldErrors.projectedTimelineMonths && (
									<p className="text-sm text-red-600 mt-1 flex items-center">
										<span className="mr-1">⚠️</span>
										{fieldErrors.projectedTimelineMonths}
									</p>
								)}
								{formData.projectedTimelineMonths &&
									!fieldErrors.projectedTimelineMonths && (
										<p className="text-sm text-gray-500 mt-1">
											{parseFloat(formData.projectedTimelineMonths) || 0} months
										</p>
									)}
							</div>
							<div className="space-y-2">
								<label className="block text-sm font-medium text-gray-700 mb-2">
									Market Cap Rate (%)
								</label>
								<input
									type="text"
									placeholder="6.5"
									value={formData.marketCapRate}
									onChange={(e) =>
										handleInputChange("marketCapRate", e.target.value)
									}
									className={`w-full px-4 py-3 text-lg border rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent ${
										fieldErrors.marketCapRate
											? "border-red-300 bg-red-50"
											: "border-gray-200 bg-white hover:border-gray-300"
									}`}
								/>
								{fieldErrors.marketCapRate && (
									<p className="text-sm text-red-600 mt-1 flex items-center">
										<span className="mr-1">⚠️</span>
										{fieldErrors.marketCapRate}
									</p>
								)}
								{formData.marketCapRate && !fieldErrors.marketCapRate && (
									<p className="text-sm text-gray-500 mt-1">
										{(parseFloat(formData.marketCapRate) || 0).toFixed(2)}%
									</p>
								)}
							</div>
						</div>
					</div>

					{/* Error Message */}
					{error && (
						<div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-xl">
							<p className="text-sm text-red-700 flex items-center">
								<span className="mr-2">⚠️</span>
								{error}
							</p>
						</div>
					)}

					{/* Action Buttons */}
					<div className="flex flex-col sm:flex-row gap-4 pt-6 border-t border-gray-100">
						<button
							type="submit"
							disabled={isCalculating}
							className="flex-1 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-medium py-4 px-6 rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:cursor-not-allowed"
						>
							{isCalculating ? (
								<span className="flex items-center justify-center">
									<svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
										<circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
										<path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
									</svg>
									Calculating...
								</span>
							) : (
								"Calculate Analysis"
							)}
						</button>
						<button
							type="button"
							onClick={handleReset}
							className="sm:w-auto bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-4 px-6 rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
						>
							Reset
						</button>
					</div>
				</form>
			</div>

				{/* Comparative Analysis Results */}
				{result && (
					<div className="mt-8 space-y-6">
						{/* Property Metrics Comparison */}
						<div className="bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden">
							<div className="p-6 sm:p-8">
								<h2 className="text-2xl font-light text-gray-900 mb-6">
									Property Metrics Comparison
								</h2>
								<div className="overflow-x-auto">
									<table className="w-full"
										<thead>
											<tr className="border-b border-gray-100">
												<th className="text-left py-4 px-2 text-sm font-medium text-gray-700">
													Metric
												</th>
												<th className="text-right py-4 px-2 text-sm font-medium text-gray-700">
													Current
												</th>
												<th className="text-right py-4 px-2 text-sm font-medium text-gray-700">
													Projected
												</th>
												<th className="text-right py-4 px-2 text-sm font-medium text-gray-700">
													Difference
												</th>
												<th className="text-right py-4 px-2 text-sm font-medium text-gray-700">
													% Change
												</th>
											</tr>
										</thead>
										<tbody className="divide-y divide-gray-50">
											{/* NOI Row */}
											<tr className="hover:bg-gray-50 transition-colors">
												<td className="py-4 px-2 text-sm font-medium text-gray-900">
													Net Operating Income
												</td>
												<td className="py-4 px-2 text-sm text-right text-gray-700">
													{new Intl.NumberFormat("en-US", {
														style: "currency",
														currency: "USD",
														minimumFractionDigits: 0,
													}).format(result.noiComparison.current)}
												</td>
												<td className="py-4 px-2 text-sm text-right text-gray-700">
													{new Intl.NumberFormat("en-US", {
														style: "currency",
														currency: "USD",
														minimumFractionDigits: 0,
													}).format(result.noiComparison.projected)}
												</td>
												<td className={`py-4 px-2 text-sm text-right font-medium ${
													result.noiComparison.difference >= 0
														? "text-green-600"
														: "text-red-600"
												}`}>
													{result.noiComparison.difference >= 0 ? "+" : ""}
													{new Intl.NumberFormat("en-US", {
														style: "currency",
														currency: "USD",
														minimumFractionDigits: 0,
													}).format(result.noiComparison.difference)}
												</td>
												<td className={`py-4 px-2 text-sm text-right font-medium ${
													result.noiComparison.percentageChange >= 0
														? "text-green-600"
														: "text-red-600"
												}`}>
													{result.noiComparison.percentageChange >= 0 ? "+" : ""}
													{result.noiComparison.percentageChange.toFixed(1)}%
												</td>
											</tr>

											{/* Cap Rate Row */}
											<tr className="hover:bg-gray-50 transition-colors">
												<td className="py-4 px-2 text-sm font-medium text-gray-900">
													Cap Rate
												</td>
												<td className="py-4 px-2 text-sm text-right text-gray-700">
													{result.capRateComparison.current.toFixed(2)}%
												</td>
												<td className="py-4 px-2 text-sm text-right text-gray-700">
													{result.capRateComparison.projected.toFixed(2)}%
												</td>
												<td className={`py-4 px-2 text-sm text-right font-medium ${
													result.capRateComparison.difference >= 0
														? "text-green-600"
														: "text-red-600"
												}`}>
													{result.capRateComparison.difference >= 0 ? "+" : ""}
													{result.capRateComparison.difference.toFixed(2)}%
												</td>
												<td className={`py-4 px-2 text-sm text-right font-medium ${
													result.capRateComparison.percentageChange >= 0
														? "text-green-600"
														: "text-red-600"
												}`}>
													{result.capRateComparison.percentageChange >= 0 ? "+" : ""}
													{result.capRateComparison.percentageChange.toFixed(1)}%
												</td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>

						{/* Investment ROI Analysis */}
						<div className="bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden">
							<div className="p-6 sm:p-8">
								<h2 className="text-2xl font-light text-gray-900 mb-6">
									Investment ROI Analysis
								</h2>
								<div className="grid grid-cols-1 sm:grid-cols-3 gap-6">
									<div className="bg-gray-50 rounded-xl p-6">
										<h3 className="text-sm font-medium text-gray-600 mb-2">
											Annual ROI
										</h3>
										<p className={`text-3xl font-light mb-2 ${
											result.roiAnalysis.roi >= 15
												? "text-green-600"
												: result.roiAnalysis.roi >= 8
													? "text-yellow-600"
													: "text-red-600"
										}`}>
											{result.roiAnalysis.roi.toFixed(1)}%
										</p>
										<p className="text-xs text-gray-500">
											Target: 15%+ Excellent, 8%+ Good
										</p>
									</div>
									<div className="bg-gray-50 rounded-xl p-6">
										<h3 className="text-sm font-medium text-gray-600 mb-2">
											Payback Period
										</h3>
										<p className="text-3xl font-light text-gray-900 mb-2">
											{result.roiAnalysis.paybackPeriodYears.toFixed(1)} years
										</p>
										<p className="text-xs text-gray-500">
											Time to recover investment
										</p>
									</div>
									<div className="bg-gray-50 rounded-xl p-6">
										<h3 className="text-sm font-medium text-gray-600 mb-2">
											10-Year NPV
										</h3>
										<p className={`text-3xl font-light mb-2 ${
											result.roiAnalysis.netPresentValue >= 0
												? "text-green-600"
												: "text-red-600"
										}`}>
											{new Intl.NumberFormat("en-US", {
												style: "currency",
												currency: "USD",
												minimumFractionDigits: 0,
												maximumFractionDigits: 0,
											}).format(result.roiAnalysis.netPresentValue)}
										</p>
										<p className="text-xs text-gray-500">
											Net present value over 10 years
										</p>
									</div>
								</div>
							</div>
						</div>

					{/* Property Valuation Impact */}
					<div className="bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden">
						<div className="p-6 sm:p-8">
							<h2 className="text-2xl font-light text-gray-900 mb-6">
								Property Valuation Impact
							</h2>
							<div className="grid grid-cols-1 sm:grid-cols-3 gap-6">
								<div className="bg-gray-50 rounded-xl p-6">
									<h3 className="text-sm font-medium text-gray-600 mb-2">
										Current Property Value
									</h3>
									<p className="text-3xl font-light text-gray-900 mb-2">
										{new Intl.NumberFormat("en-US", {
											style: "currency",
											currency: "USD",
											minimumFractionDigits: 0,
											maximumFractionDigits: 0,
										}).format(result.propertyValuation.currentValue)}
									</p>
									<p className="text-xs text-gray-500">
										Based on current NOI and market cap rate
									</p>
								</div>
								<div className="bg-gray-50 rounded-xl p-6">
									<h3 className="text-sm font-medium text-gray-600 mb-2">
										Projected Property Value
									</h3>
									<p className="text-3xl font-light text-gray-900 mb-2">
										{new Intl.NumberFormat("en-US", {
											style: "currency",
											currency: "USD",
											minimumFractionDigits: 0,
											maximumFractionDigits: 0,
										}).format(result.propertyValuation.projectedValue)}
									</p>
									<p className="text-xs text-gray-500">
										After improvements and NOI increase
									</p>
								</div>
								<div className="bg-gray-50 rounded-xl p-6">
									<h3 className="text-sm font-medium text-gray-600 mb-2">
										Equity Growth
									</h3>
									<p className="text-3xl font-light text-green-600 mb-2">
										+{new Intl.NumberFormat("en-US", {
											style: "currency",
											currency: "USD",
											minimumFractionDigits: 0,
											maximumFractionDigits: 0,
										}).format(result.propertyValuation.valueIncrease)}
									</p>
									<p className="text-xs text-gray-500">
										Increase in property value from improvements
									</p>
								</div>
							</div>
						</div>
					</div>

					{/* Cash Flow Analysis */}
					<div className="bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden">
						<div className="p-6 sm:p-8">
							<h2 className="text-2xl font-light text-gray-900 mb-6">
								Cash Flow Analysis
							</h2>
							<div className="grid grid-cols-1 sm:grid-cols-3 gap-6">
								<div className="bg-gray-50 rounded-xl p-6">
									<h3 className="text-sm font-medium text-gray-600 mb-2">
										Monthly Cash Flow Increase
									</h3>
									<p className="text-3xl font-light text-green-600 mb-2">
										+{new Intl.NumberFormat("en-US", {
											style: "currency",
											currency: "USD",
											minimumFractionDigits: 0,
											maximumFractionDigits: 0,
										}).format(result.cashFlowAnalysis.monthlyIncrease)}
									</p>
									<p className="text-xs text-gray-500">
										Additional monthly income
									</p>
								</div>
								<div className="bg-gray-50 rounded-xl p-6">
									<h3 className="text-sm font-medium text-gray-600 mb-2">
										Annual Cash Flow Increase
									</h3>
									<p className="text-3xl font-light text-green-600 mb-2">
										+{new Intl.NumberFormat("en-US", {
											style: "currency",
											currency: "USD",
											minimumFractionDigits: 0,
											maximumFractionDigits: 0,
										}).format(result.cashFlowAnalysis.annualIncrease)}
									</p>
									<p className="text-xs text-gray-500">
										Additional yearly income
									</p>
								</div>
								<div className="bg-gray-50 rounded-xl p-6">
									<h3 className="text-sm font-medium text-gray-600 mb-2">
										5-Year Cash Flow Benefit
									</h3>
									<p className="text-3xl font-light text-green-600 mb-2">
										+{new Intl.NumberFormat("en-US", {
											style: "currency",
											currency: "USD",
											minimumFractionDigits: 0,
											maximumFractionDigits: 0,
										}).format(result.cashFlowAnalysis.cumulativeIncrease5Years)}
									</p>
									<p className="text-xs text-gray-500">
										Total additional income over 5 years
									</p>
								</div>
							</div>
						</div>
					</div>

					{/* Reset Button */}
					<div className="mt-6 text-center">
						<button
							onClick={handleReset}
							className="bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-3 px-8 rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
						>
							Reset Calculator
						</button>
					</div>
				</div>
			)}

			{/* Information Card */}
			<div className="mt-8 bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden">
				<div className="p-6 sm:p-8">
					<h2 className="text-2xl font-light text-gray-900 mb-4">
						About This Calculator
					</h2>
					<p className="text-gray-600 mb-6 leading-relaxed">
						This comparative deal analyzer helps you evaluate whether property
						improvements are financially worthwhile by comparing current vs
						projected performance across multiple metrics.
					</p>
					<div className="space-y-4">
						<div className="flex items-start space-x-3">
							<span className="text-green-600 mt-1">•</span>
							<p className="text-gray-600">
								<span className="font-medium text-gray-900">Property Metrics Comparison:</span> Side-by-side
								comparison of NOI and cap rates
							</p>
						</div>
						<div className="flex items-start space-x-3">
							<span className="text-green-600 mt-1">•</span>
							<p className="text-gray-600">
								<span className="font-medium text-gray-900">Investment ROI Analysis:</span> Calculates annual ROI,
								payback period, and 10-year net present value
							</p>
						</div>
						<div className="flex items-start space-x-3">
							<span className="text-green-600 mt-1">•</span>
							<p className="text-gray-600">
								<span className="font-medium text-gray-900">Property Valuation Impact:</span> Shows how improvements
								affect property value and equity growth
							</p>
						</div>
						<div className="flex items-start space-x-3">
							<span className="text-green-600 mt-1">•</span>
							<p className="text-gray-600">
								<span className="font-medium text-gray-900">Cash Flow Improvement:</span> Analyzes monthly, annual,
								and long-term cash flow benefits
							</p>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
