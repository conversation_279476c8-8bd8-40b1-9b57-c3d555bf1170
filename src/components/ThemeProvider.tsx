import React from "react";
import { Theme } from "@radix-ui/themes";

interface ThemeProviderProps {
	children: React.ReactNode;
	accentColor?: Parameters<typeof Theme>[0]["accentColor"];
	grayColor?: Parameters<typeof Theme>[0]["grayColor"];
	radius?: Parameters<typeof Theme>[0]["radius"];
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({
	children,
	accentColor = "gray",
	grayColor = "gray",
	radius = "medium",
}) => {
	return (
		<Theme accentColor={accentColor} grayColor={grayColor} radius={radius}>
			{children}
		</Theme>
	);
};
