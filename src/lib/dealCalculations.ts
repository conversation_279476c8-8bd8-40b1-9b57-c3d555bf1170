/**
 * Comparative Deal Analyzer Logic
 *
 * Comprehensive real estate investment analysis comparing:
 * - Current Property State vs Projected Property State
 * - ROI Analysis for Improvements/Changes
 * - Payback Period Calculations
 * - Cash Flow Improvements
 * - Cap Rate Improvements
 */

// Current property state inputs
export interface CurrentPropertyState {
	purchasePrice: number;
	currentNOI: number;
	currentGrossIncome: number;
	currentExpenses: number;
	currentCapRate?: number; // Optional, will be calculated if not provided
}

// Projected property state inputs (after improvements/changes)
export interface ProjectedPropertyState {
	projectedNOI: number;
	projectedGrossIncome: number;
	projectedExpenses: number;
	improvementCost: number;
	projectedTimelineMonths: number; // Timeline to complete improvements
	marketCapRate: number; // Market cap rate for valuation
}

// Combined input for comparative analysis
export interface ComparativeAnalysisInput {
	current: CurrentPropertyState;
	projected: ProjectedPropertyState;
}

// Individual metric comparison
export interface MetricComparison {
	current: number;
	projected: number;
	difference: number;
	percentageChange: number;
}

// ROI and payback analysis
export interface ROIAnalysis {
	totalInvestment: number;
	annualCashFlowIncrease: number;
	roi: number; // Annual ROI percentage
	paybackPeriodYears: number;
	netPresentValue: number; // NPV over 10 years at 6% discount rate
	analysis: string;
}

// Property valuation comparison
export interface PropertyValuation {
	currentValue: number;
	projectedValue: number;
	valueIncrease: number;
	valueIncreasePercentage: number;
	analysis: string;
}

// Cash flow analysis
export interface CashFlowAnalysis {
	monthlyIncrease: number;
	annualIncrease: number;
	cumulativeIncrease5Years: number;
	analysis: string;
}

// Complete comparative analysis result
export interface ComparativeAnalysisResult {
	noiComparison: MetricComparison;
	grossIncomeComparison: MetricComparison;
	expensesComparison: MetricComparison;
	capRateComparison: MetricComparison;
	roiAnalysis: ROIAnalysis;
	propertyValuation: PropertyValuation;
	cashFlowAnalysis: CashFlowAnalysis;
}

// Legacy interfaces for backward compatibility (will be deprecated)
export interface DealAnalysisInput {
	currentPurchasePrice: number;
	currentNOI: number;
	improvementCost: number;
	noiIncrease: number;
	marketCapRate: number;
	recessionOccupancyRate: number;
}

export interface CapRateAnalysis {
	currentCapRate: number;
	marketCapRate: number;
	analysis: string;
}

export interface ImprovementAnalysis {
	roi: number;
	newPropertyValue: number;
	newNOI: number;
	analysis: string;
}

export interface RecessionSensitivity {
	recessionNOI: number;
	recessionCapRate: number;
	analysis: string;
}

export interface DealAnalysisResult {
	capRate: CapRateAnalysis;
	improvements: ImprovementAnalysis;
	recession: RecessionSensitivity;
}

/**
 * Calculates and analyzes the cap rate
 */
export function calculateCapRate(input: DealAnalysisInput): CapRateAnalysis {
	const { currentPurchasePrice, currentNOI, marketCapRate } = input;

	// Calculate current cap rate
	const currentCapRate = (currentNOI / currentPurchasePrice) * 100;

	// Generate analysis
	let analysis = "";
	if (currentCapRate > marketCapRate) {
		analysis = `Your property's cap rate of ${currentCapRate.toFixed(2)}% is above the market rate of ${marketCapRate.toFixed(2)}%. This suggests the property may be undervalued or generating strong income relative to its price.`;
	} else if (currentCapRate < marketCapRate) {
		analysis = `Your property's cap rate of ${currentCapRate.toFixed(2)}% is below the market rate of ${marketCapRate.toFixed(2)}%. This could indicate the property is overvalued or has room for income improvement.`;
	} else {
		analysis = `Your property's cap rate of ${currentCapRate.toFixed(2)}% matches the market rate of ${marketCapRate.toFixed(2)}%. The property appears to be priced in line with market conditions.`;
	}

	return {
		currentCapRate,
		marketCapRate,
		analysis,
	};
}

/**
 * Calculates NOI (Net Operating Income)
 */
export function calculateNOI(
	grossIncome: number,
	operatingExpenses: number,
): number {
	return grossIncome - operatingExpenses;
}

/**
 * Analyzes improvement ROI and its impact on property value
 */
export function calculateImprovementROI(
	input: DealAnalysisInput,
): ImprovementAnalysis {
	const { currentNOI, improvementCost, noiIncrease, marketCapRate } = input;

	// Calculate new NOI after improvements
	const newNOI = currentNOI + noiIncrease;

	// Calculate ROI on improvements (annual return / cost)
	const roi = improvementCost > 0 ? (noiIncrease / improvementCost) * 100 : 0;

	// Calculate new property value based on market cap rate
	const newPropertyValue = newNOI / (marketCapRate / 100);

	// Generate analysis
	let analysis = "";
	if (roi > 20) {
		analysis = `Excellent improvement ROI of ${roi.toFixed(1)}%! The improvements will increase your property value to ${new Intl.NumberFormat("en-US", { style: "currency", currency: "USD", minimumFractionDigits: 0 }).format(newPropertyValue)} and generate strong returns.`;
	} else if (roi > 10) {
		analysis = `Good improvement ROI of ${roi.toFixed(1)}%. The improvements will increase your property value to ${new Intl.NumberFormat("en-US", { style: "currency", currency: "USD", minimumFractionDigits: 0 }).format(newPropertyValue)} and provide solid returns.`;
	} else if (roi > 0) {
		analysis = `Moderate improvement ROI of ${roi.toFixed(1)}%. The improvements will increase your property value to ${new Intl.NumberFormat("en-US", { style: "currency", currency: "USD", minimumFractionDigits: 0 }).format(newPropertyValue)} but may take longer to pay off.`;
	} else {
		analysis = `The improvements don't provide a positive ROI. Consider whether the improvements are necessary for property maintenance or if there are more cost-effective alternatives.`;
	}

	return {
		roi,
		newPropertyValue,
		newNOI,
		analysis,
	};
}

/**
 * Tests recession sensitivity based on historical occupancy rates
 */
export function calculateRecessionSensitivity(
	input: DealAnalysisInput,
): RecessionSensitivity {
	const { currentNOI, recessionOccupancyRate } = input;

	// Calculate recession NOI based on occupancy rate
	// Assume 100% occupancy is the baseline
	const recessionNOI = currentNOI * (recessionOccupancyRate / 100);

	// Calculate recession cap rate (assuming purchase price stays the same)
	const recessionCapRate = (recessionNOI / input.currentPurchasePrice) * 100;

	// Generate analysis
	let analysis = "";
	if (recessionOccupancyRate >= 90) {
		analysis = `Strong recession resilience! With ${recessionOccupancyRate}% occupancy during recessions, your NOI would only drop to ${new Intl.NumberFormat("en-US", { style: "currency", currency: "USD", minimumFractionDigits: 0 }).format(recessionNOI)}. The property shows excellent stability during economic downturns.`;
	} else if (recessionOccupancyRate >= 80) {
		analysis = `Good recession resilience. With ${recessionOccupancyRate}% occupancy during recessions, your NOI would drop to ${new Intl.NumberFormat("en-US", { style: "currency", currency: "USD", minimumFractionDigits: 0 }).format(recessionNOI)}. The property should weather most economic downturns well.`;
	} else if (recessionOccupancyRate >= 70) {
		analysis = `Moderate recession risk. With ${recessionOccupancyRate}% occupancy during recessions, your NOI would drop to ${new Intl.NumberFormat("en-US", { style: "currency", currency: "USD", minimumFractionDigits: 0 }).format(recessionNOI)}. Consider building cash reserves for economic downturns.`;
	} else {
		analysis = `High recession risk. With ${recessionOccupancyRate}% occupancy during recessions, your NOI would drop significantly to ${new Intl.NumberFormat("en-US", { style: "currency", currency: "USD", minimumFractionDigits: 0 }).format(recessionNOI)}. This property may be vulnerable during economic downturns.`;
	}

	return {
		recessionNOI,
		recessionCapRate,
		analysis,
	};
}

// ============================================================================
// NEW COMPARATIVE ANALYSIS FUNCTIONS
// ============================================================================

/**
 * Helper function to create metric comparison
 */
function createMetricComparison(
	current: number,
	projected: number,
): MetricComparison {
	const difference = projected - current;
	const percentageChange = current !== 0 ? (difference / current) * 100 : 0;

	return {
		current,
		projected,
		difference,
		percentageChange,
	};
}

/**
 * Calculate Net Present Value (NPV) for cash flow improvements
 */
function calculateNPV(
	annualCashFlow: number,
	years: number = 10,
	discountRate: number = 0.06,
): number {
	let npv = 0;
	for (let year = 1; year <= years; year++) {
		npv += annualCashFlow / Math.pow(1 + discountRate, year);
	}
	return npv;
}

/**
 * Main comparative analysis function
 */
export function calculateComparativeAnalysis(
	input: ComparativeAnalysisInput,
): ComparativeAnalysisResult {
	const { current, projected } = input;

	// Calculate current cap rate if not provided
	const currentCapRate =
		current.currentCapRate ||
		(current.currentNOI / current.purchasePrice) * 100;

	// Calculate projected cap rate based on original purchase price
	const projectedCapRate =
		(projected.projectedNOI / current.purchasePrice) * 100;

	// Create metric comparisons
	const noiComparison = createMetricComparison(
		current.currentNOI,
		projected.projectedNOI,
	);
	const grossIncomeComparison = createMetricComparison(
		current.currentGrossIncome,
		projected.projectedGrossIncome,
	);
	const expensesComparison = createMetricComparison(
		current.currentExpenses,
		projected.projectedExpenses,
	);
	const capRateComparison = createMetricComparison(
		currentCapRate,
		projectedCapRate,
	);

	// Calculate ROI Analysis
	const annualCashFlowIncrease = projected.projectedNOI - current.currentNOI;
	const roi =
		projected.improvementCost > 0
			? (annualCashFlowIncrease / projected.improvementCost) * 100
			: 0;
	const paybackPeriodYears =
		projected.improvementCost > 0
			? projected.improvementCost / annualCashFlowIncrease
			: 0;
	const netPresentValue =
		calculateNPV(annualCashFlowIncrease) - projected.improvementCost;

	// Generate ROI analysis text
	let roiAnalysisText = "";
	if (roi > 25) {
		roiAnalysisText = `Outstanding ROI of ${roi.toFixed(1)}%! This investment will pay for itself in ${paybackPeriodYears.toFixed(1)} years and generate excellent long-term returns.`;
	} else if (roi > 15) {
		roiAnalysisText = `Strong ROI of ${roi.toFixed(1)}%. The investment will pay back in ${paybackPeriodYears.toFixed(1)} years with solid returns.`;
	} else if (roi > 8) {
		roiAnalysisText = `Moderate ROI of ${roi.toFixed(1)}%. The payback period is ${paybackPeriodYears.toFixed(1)} years, which may be acceptable depending on your investment goals.`;
	} else if (roi > 0) {
		roiAnalysisText = `Low ROI of ${roi.toFixed(1)}%. The ${paybackPeriodYears.toFixed(1)}-year payback period suggests this investment may not be optimal.`;
	} else {
		roiAnalysisText = `Negative ROI. The projected improvements would not generate sufficient additional income to justify the investment cost.`;
	}

	const roiAnalysis: ROIAnalysis = {
		totalInvestment: projected.improvementCost,
		annualCashFlowIncrease,
		roi,
		paybackPeriodYears,
		netPresentValue,
		analysis: roiAnalysisText,
	};

	// Calculate Property Valuation
	const currentValue = current.currentNOI / (projected.marketCapRate / 100);
	const projectedValue =
		projected.projectedNOI / (projected.marketCapRate / 100);
	const valueIncrease = projectedValue - currentValue;
	const valueIncreasePercentage =
		currentValue > 0 ? (valueIncrease / currentValue) * 100 : 0;

	let valuationAnalysisText = "";
	if (valueIncreasePercentage > 20) {
		valuationAnalysisText = `Excellent value creation! The improvements are projected to increase property value by ${valueIncreasePercentage.toFixed(1)}% (${new Intl.NumberFormat("en-US", { style: "currency", currency: "USD", minimumFractionDigits: 0 }).format(valueIncrease)}), significantly outpacing the investment cost.`;
	} else if (valueIncreasePercentage > 10) {
		valuationAnalysisText = `Good value creation. The property value should increase by ${valueIncreasePercentage.toFixed(1)}% (${new Intl.NumberFormat("en-US", { style: "currency", currency: "USD", minimumFractionDigits: 0 }).format(valueIncrease)}), providing solid equity growth.`;
	} else if (valueIncreasePercentage > 0) {
		valuationAnalysisText = `Modest value increase of ${valueIncreasePercentage.toFixed(1)}% (${new Intl.NumberFormat("en-US", { style: "currency", currency: "USD", minimumFractionDigits: 0 }).format(valueIncrease)}). Consider if the cash flow benefits justify the investment.`;
	} else {
		valuationAnalysisText = `The improvements may not significantly increase property value based on current market cap rates. Focus on cash flow benefits rather than appreciation.`;
	}

	const propertyValuation: PropertyValuation = {
		currentValue,
		projectedValue,
		valueIncrease,
		valueIncreasePercentage,
		analysis: valuationAnalysisText,
	};

	// Calculate Cash Flow Analysis
	const monthlyIncrease = annualCashFlowIncrease / 12;
	const cumulativeIncrease5Years = annualCashFlowIncrease * 5;

	let cashFlowAnalysisText = "";
	if (monthlyIncrease > 1000) {
		cashFlowAnalysisText = `Substantial cash flow improvement of ${new Intl.NumberFormat("en-US", { style: "currency", currency: "USD", minimumFractionDigits: 0 }).format(monthlyIncrease)} per month. Over 5 years, this represents ${new Intl.NumberFormat("en-US", { style: "currency", currency: "USD", minimumFractionDigits: 0 }).format(cumulativeIncrease5Years)} in additional cash flow.`;
	} else if (monthlyIncrease > 500) {
		cashFlowAnalysisText = `Good cash flow improvement of ${new Intl.NumberFormat("en-US", { style: "currency", currency: "USD", minimumFractionDigits: 0 }).format(monthlyIncrease)} per month, totaling ${new Intl.NumberFormat("en-US", { style: "currency", currency: "USD", minimumFractionDigits: 0 }).format(cumulativeIncrease5Years)} over 5 years.`;
	} else if (monthlyIncrease > 0) {
		cashFlowAnalysisText = `Modest cash flow improvement of ${new Intl.NumberFormat("en-US", { style: "currency", currency: "USD", minimumFractionDigits: 0 }).format(monthlyIncrease)} per month. The 5-year cumulative benefit is ${new Intl.NumberFormat("en-US", { style: "currency", currency: "USD", minimumFractionDigits: 0 }).format(cumulativeIncrease5Years)}.`;
	} else {
		cashFlowAnalysisText = `The projected changes would not improve monthly cash flow. Reconsider the investment strategy or look for cost reduction opportunities.`;
	}

	const cashFlowAnalysis: CashFlowAnalysis = {
		monthlyIncrease,
		annualIncrease: annualCashFlowIncrease,
		cumulativeIncrease5Years,
		analysis: cashFlowAnalysisText,
	};

	return {
		noiComparison,
		grossIncomeComparison,
		expensesComparison,
		capRateComparison,
		roiAnalysis,
		propertyValuation,
		cashFlowAnalysis,
	};
}
