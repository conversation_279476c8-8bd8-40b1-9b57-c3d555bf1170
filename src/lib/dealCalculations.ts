/**
 * Full Deal Analyzer Logic
 *
 * Comprehensive real estate investment analysis including:
 * - Cap Rate Analysis
 * - Improvement ROI Analysis
 * - Recession Sensitivity Testing
 */

export interface DealAnalysisInput {
	currentPurchasePrice: number;
	currentNOI: number;
	improvementCost: number;
	noiIncrease: number;
	marketCapRate: number;
	recessionOccupancyRate: number;
}

export interface CapRateAnalysis {
	currentCapRate: number;
	marketCapRate: number;
	analysis: string;
}

export interface ImprovementAnalysis {
	roi: number;
	newPropertyValue: number;
	newNOI: number;
	analysis: string;
}

export interface RecessionSensitivity {
	recessionNOI: number;
	recessionCapRate: number;
	analysis: string;
}

export interface DealAnalysisResult {
	capRate: CapRateAnalysis;
	improvements: ImprovementAnalysis;
	recession: RecessionSensitivity;
}

/**
 * Calculates and analyzes the cap rate
 */
export function calculateCapRate(input: DealAnalysisInput): CapRateAnalysis {
	const { currentPurchasePrice, currentNOI, marketCapRate } = input;

	// Calculate current cap rate
	const currentCapRate = (currentNOI / currentPurchasePrice) * 100;

	// Generate analysis
	let analysis = "";
	if (currentCapRate > marketCapRate) {
		analysis = `Your property's cap rate of ${currentCapRate.toFixed(2)}% is above the market rate of ${marketCapRate.toFixed(2)}%. This suggests the property may be undervalued or generating strong income relative to its price.`;
	} else if (currentCapRate < marketCapRate) {
		analysis = `Your property's cap rate of ${currentCapRate.toFixed(2)}% is below the market rate of ${marketCapRate.toFixed(2)}%. This could indicate the property is overvalued or has room for income improvement.`;
	} else {
		analysis = `Your property's cap rate of ${currentCapRate.toFixed(2)}% matches the market rate of ${marketCapRate.toFixed(2)}%. The property appears to be priced in line with market conditions.`;
	}

	return {
		currentCapRate,
		marketCapRate,
		analysis,
	};
}

/**
 * Calculates NOI (Net Operating Income)
 */
export function calculateNOI(
	grossIncome: number,
	operatingExpenses: number,
): number {
	return grossIncome - operatingExpenses;
}

/**
 * Analyzes improvement ROI and its impact on property value
 */
export function calculateImprovementROI(
	input: DealAnalysisInput,
): ImprovementAnalysis {
	const { currentNOI, improvementCost, noiIncrease, marketCapRate } = input;

	// Calculate new NOI after improvements
	const newNOI = currentNOI + noiIncrease;

	// Calculate ROI on improvements (annual return / cost)
	const roi = improvementCost > 0 ? (noiIncrease / improvementCost) * 100 : 0;

	// Calculate new property value based on market cap rate
	const newPropertyValue = newNOI / (marketCapRate / 100);

	// Generate analysis
	let analysis = "";
	if (roi > 20) {
		analysis = `Excellent improvement ROI of ${roi.toFixed(1)}%! The improvements will increase your property value to ${new Intl.NumberFormat("en-US", { style: "currency", currency: "USD", minimumFractionDigits: 0 }).format(newPropertyValue)} and generate strong returns.`;
	} else if (roi > 10) {
		analysis = `Good improvement ROI of ${roi.toFixed(1)}%. The improvements will increase your property value to ${new Intl.NumberFormat("en-US", { style: "currency", currency: "USD", minimumFractionDigits: 0 }).format(newPropertyValue)} and provide solid returns.`;
	} else if (roi > 0) {
		analysis = `Moderate improvement ROI of ${roi.toFixed(1)}%. The improvements will increase your property value to ${new Intl.NumberFormat("en-US", { style: "currency", currency: "USD", minimumFractionDigits: 0 }).format(newPropertyValue)} but may take longer to pay off.`;
	} else {
		analysis = `The improvements don't provide a positive ROI. Consider whether the improvements are necessary for property maintenance or if there are more cost-effective alternatives.`;
	}

	return {
		roi,
		newPropertyValue,
		newNOI,
		analysis,
	};
}

/**
 * Tests recession sensitivity based on historical occupancy rates
 */
export function calculateRecessionSensitivity(
	input: DealAnalysisInput,
): RecessionSensitivity {
	const { currentNOI, recessionOccupancyRate } = input;

	// Calculate recession NOI based on occupancy rate
	// Assume 100% occupancy is the baseline
	const recessionNOI = currentNOI * (recessionOccupancyRate / 100);

	// Calculate recession cap rate (assuming purchase price stays the same)
	const recessionCapRate = (recessionNOI / input.currentPurchasePrice) * 100;

	// Generate analysis
	let analysis = "";
	if (recessionOccupancyRate >= 90) {
		analysis = `Strong recession resilience! With ${recessionOccupancyRate}% occupancy during recessions, your NOI would only drop to ${new Intl.NumberFormat("en-US", { style: "currency", currency: "USD", minimumFractionDigits: 0 }).format(recessionNOI)}. The property shows excellent stability during economic downturns.`;
	} else if (recessionOccupancyRate >= 80) {
		analysis = `Good recession resilience. With ${recessionOccupancyRate}% occupancy during recessions, your NOI would drop to ${new Intl.NumberFormat("en-US", { style: "currency", currency: "USD", minimumFractionDigits: 0 }).format(recessionNOI)}. The property should weather most economic downturns well.`;
	} else if (recessionOccupancyRate >= 70) {
		analysis = `Moderate recession risk. With ${recessionOccupancyRate}% occupancy during recessions, your NOI would drop to ${new Intl.NumberFormat("en-US", { style: "currency", currency: "USD", minimumFractionDigits: 0 }).format(recessionNOI)}. Consider building cash reserves for economic downturns.`;
	} else {
		analysis = `High recession risk. With ${recessionOccupancyRate}% occupancy during recessions, your NOI would drop significantly to ${new Intl.NumberFormat("en-US", { style: "currency", currency: "USD", minimumFractionDigits: 0 }).format(recessionNOI)}. This property may be vulnerable during economic downturns.`;
	}

	return {
		recessionNOI,
		recessionCapRate,
		analysis,
	};
}
