import { describe, it, expect } from "vitest";
import {
	calculateOnePercentRule,
	validateInput,
	formatCurrency,
	formatPercentage,
	parseCurrency,
	formatCurrencyInput,
	type OnePercentRuleInput,
} from "./onePercentRule";

describe("onePercentRule", () => {
	describe("calculateOnePercentRule", () => {
		it("should identify a good deal when rent meets 1% rule", () => {
			const input: OnePercentRuleInput = {
				purchasePrice: 100000,
				monthlyRent: 1000,
			};

			const result = calculateOnePercentRule(input);

			expect(result.isGoodDeal).toBe(true);
			expect(result.recommendation).toBe("DEAL");
			expect(result.rentToPrice).toBe(1.0);
			expect(result.explanation).toContain("meets the 1% rule");
		});

		it("should identify a bad deal when rent is below 1% rule", () => {
			const input: OnePercentRuleInput = {
				purchasePrice: 100000,
				monthlyRent: 800,
			};

			const result = calculateOnePercentRule(input);

			expect(result.isGoodDeal).toBe(false);
			expect(result.recommendation).toBe("NO_DEAL");
			expect(result.rentToPrice).toBe(0.8);
			expect(result.explanation).toContain("doesn't meet the 1% rule");
		});

		it("should handle deals that exceed the 1% rule", () => {
			const input: OnePercentRuleInput = {
				purchasePrice: 100000,
				monthlyRent: 1500,
			};

			const result = calculateOnePercentRule(input);

			expect(result.isGoodDeal).toBe(true);
			expect(result.recommendation).toBe("DEAL");
			expect(result.rentToPrice).toBe(1.5);
		});

		it("should handle edge case exactly at 1%", () => {
			const input: OnePercentRuleInput = {
				purchasePrice: 250000,
				monthlyRent: 2500,
			};

			const result = calculateOnePercentRule(input);

			expect(result.isGoodDeal).toBe(true);
			expect(result.recommendation).toBe("DEAL");
			expect(result.rentToPrice).toBe(1.0);
		});

		it("should handle decimal values correctly", () => {
			const input: OnePercentRuleInput = {
				purchasePrice: 175000,
				monthlyRent: 1750.5,
			};

			const result = calculateOnePercentRule(input);

			expect(result.isGoodDeal).toBe(true);
			expect(result.rentToPrice).toBeCloseTo(1.0003, 4);
		});
	});

	describe("validateInput", () => {
		it("should return null for valid inputs", () => {
			const error = validateInput(100000, 1000);
			expect(error).toBeNull();
		});

		it("should reject zero purchase price", () => {
			const error = validateInput(0, 1000);
			expect(error).toBe("Purchase price must be greater than $0");
		});

		it("should reject negative purchase price", () => {
			const error = validateInput(-100000, 1000);
			expect(error).toBe("Purchase price must be greater than $0");
		});

		it("should reject zero monthly rent", () => {
			const error = validateInput(100000, 0);
			expect(error).toBe("Monthly rent must be greater than $0");
		});

		it("should reject negative monthly rent", () => {
			const error = validateInput(100000, -1000);
			expect(error).toBe("Monthly rent must be greater than $0");
		});

		it("should reject unreasonably high purchase price", () => {
			const error = validateInput(200000000, 1000);
			expect(error).toBe("Purchase price seems unreasonably high");
		});

		it("should reject unreasonably high monthly rent", () => {
			const error = validateInput(100000, 2000000);
			expect(error).toBe("Monthly rent seems unreasonably high");
		});

		it("should accept high but reasonable values", () => {
			const error = validateInput(50000000, 500000);
			expect(error).toBeNull();
		});
	});

	describe("formatCurrency", () => {
		it("should format whole numbers correctly", () => {
			expect(formatCurrency(100000)).toBe("$100,000");
			expect(formatCurrency(1000)).toBe("$1,000");
			expect(formatCurrency(500)).toBe("$500");
		});

		it("should handle zero", () => {
			expect(formatCurrency(0)).toBe("$0");
		});

		it("should round decimals to whole numbers", () => {
			expect(formatCurrency(1000.99)).toBe("$1,001");
			expect(formatCurrency(1000.49)).toBe("$1,000");
		});

		it("should handle large numbers", () => {
			expect(formatCurrency(1000000)).toBe("$1,000,000");
			expect(formatCurrency(50000000)).toBe("$50,000,000");
		});
	});

	describe("formatPercentage", () => {
		it("should format percentages correctly", () => {
			expect(formatPercentage(1.0)).toBe("1.00%");
			expect(formatPercentage(1.5)).toBe("1.50%");
			expect(formatPercentage(0.8)).toBe("0.80%");
		});

		it("should handle zero", () => {
			expect(formatPercentage(0)).toBe("0.00%");
		});

		it("should handle high precision", () => {
			expect(formatPercentage(1.2345)).toBe("1.23%");
		});
	});

	describe("parseCurrency", () => {
		it("should parse clean numbers", () => {
			expect(parseCurrency("100000")).toBe(100000);
			expect(parseCurrency("1000")).toBe(1000);
		});

		it("should parse formatted currency", () => {
			expect(parseCurrency("$100,000")).toBe(100000);
			expect(parseCurrency("$1,000.50")).toBe(1000.5);
		});

		it("should handle spaces", () => {
			expect(parseCurrency("$ 100,000")).toBe(100000);
			expect(parseCurrency("100 000")).toBe(100000);
		});

		it("should return 0 for invalid input", () => {
			expect(parseCurrency("")).toBe(0);
			expect(parseCurrency("abc")).toBe(0);
			expect(parseCurrency("$")).toBe(0);
		});

		it("should handle decimal values", () => {
			expect(parseCurrency("1000.50")).toBe(1000.5);
			expect(parseCurrency("$1,000.99")).toBe(1000.99);
		});
	});

	describe("formatCurrencyInput", () => {
		it("should format numbers with commas", () => {
			expect(formatCurrencyInput("100000")).toBe("100,000");
			expect(formatCurrencyInput("1000")).toBe("1,000");
		});

		it("should return empty string for zero", () => {
			expect(formatCurrencyInput("0")).toBe("");
			expect(formatCurrencyInput("")).toBe("");
		});

		it("should handle already formatted input", () => {
			expect(formatCurrencyInput("$100,000")).toBe("100,000");
		});

		it("should handle invalid input gracefully", () => {
			expect(formatCurrencyInput("abc")).toBe("");
		});
	});

	describe("real-world scenarios", () => {
		it("should handle typical rental property scenario", () => {
			const input: OnePercentRuleInput = {
				purchasePrice: 250000,
				monthlyRent: 2200,
			};

			const result = calculateOnePercentRule(input);

			expect(result.isGoodDeal).toBe(false);
			expect(result.rentToPrice).toBeCloseTo(0.88, 2);
			expect(result.explanation).toContain("doesn't meet the 1% rule");
		});

		it("should handle high-end property scenario", () => {
			const input: OnePercentRuleInput = {
				purchasePrice: 800000,
				monthlyRent: 6500,
			};

			const result = calculateOnePercentRule(input);

			expect(result.isGoodDeal).toBe(false);
			expect(result.rentToPrice).toBeCloseTo(0.8125, 4);
		});

		it("should handle cash flow positive scenario", () => {
			const input: OnePercentRuleInput = {
				purchasePrice: 150000,
				monthlyRent: 1800,
			};

			const result = calculateOnePercentRule(input);

			expect(result.isGoodDeal).toBe(true);
			expect(result.rentToPrice).toBe(1.2);
		});
	});
});
