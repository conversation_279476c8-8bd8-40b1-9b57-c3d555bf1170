/**
 * 1% Rule Calculator Logic
 *
 * The 1% rule states that monthly rent should be at least 1% of the purchase price
 * for a property to be considered a good investment opportunity.
 */

export interface OnePercentRuleInput {
	purchasePrice: number;
	monthlyRent: number;
}

export interface OnePercentRuleResult {
	isGoodDeal: boolean;
	rentToPrice: number; // Actual percentage (e.g., 1.2 for 1.2%)
	explanation: string;
	recommendation: "DEAL" | "NO_DEAL";
}

/**
 * Validates input values for the 1% rule calculation
 */
export function validateInput(
	purchasePrice: number,
	monthlyRent: number,
): string | null {
	if (purchasePrice <= 0) {
		return "Purchase price must be greater than $0";
	}

	if (monthlyRent <= 0) {
		return "Monthly rent must be greater than $0";
	}

	if (purchasePrice > 100000000) {
		// $100M cap for sanity
		return "Purchase price seems unreasonably high";
	}

	if (monthlyRent > 1000000) {
		// $1M monthly rent cap for sanity
		return "Monthly rent seems unreasonably high";
	}

	return null;
}

/**
 * Calculates whether a property meets the 1% rule
 */
export function calculateOnePercentRule(
	input: OnePercentRuleInput,
): OnePercentRuleResult {
	const { purchasePrice, monthlyRent } = input;

	// Calculate the actual rent-to-price ratio as a percentage
	const rentToPrice = (monthlyRent / purchasePrice) * 100;

	// Check if it meets the 1% rule
	const isGoodDeal = rentToPrice >= 1.0;

	// Generate explanation
	const explanation = isGoodDeal
		? `This property meets the 1% rule! Monthly rent (${formatCurrency(monthlyRent)}) is ${rentToPrice.toFixed(2)}% of the purchase price (${formatCurrency(purchasePrice)}).`
		: `This property doesn't meet the 1% rule. Monthly rent (${formatCurrency(monthlyRent)}) is only ${rentToPrice.toFixed(2)}% of the purchase price (${formatCurrency(purchasePrice)}). You need at least ${formatCurrency(purchasePrice * 0.01)} monthly rent.`;

	return {
		isGoodDeal,
		rentToPrice,
		explanation,
		recommendation: isGoodDeal ? "DEAL" : "NO_DEAL",
	};
}

/**
 * Formats a number as currency
 */
export function formatCurrency(amount: number): string {
	return new Intl.NumberFormat("en-US", {
		style: "currency",
		currency: "USD",
		minimumFractionDigits: 0,
		maximumFractionDigits: 0,
	}).format(amount);
}

/**
 * Formats a number as a percentage
 */
export function formatPercentage(value: number): string {
	return new Intl.NumberFormat("en-US", {
		style: "percent",
		minimumFractionDigits: 2,
		maximumFractionDigits: 2,
	}).format(value / 100);
}

/**
 * Parses a currency string to a number
 */
export function parseCurrency(value: string): number {
	// Remove currency symbols, commas, and spaces
	const cleaned = value.replace(/[$,\s]/g, "");
	const parsed = parseFloat(cleaned);
	return isNaN(parsed) ? 0 : parsed;
}

/**
 * Formats input value for display in currency fields
 */
export function formatCurrencyInput(value: string): string {
	const number = parseCurrency(value);
	if (number === 0) return "";

	return new Intl.NumberFormat("en-US").format(number);
}
