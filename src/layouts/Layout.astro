---
import '../styles/global.css';

export interface Props {
  title: string;
  description?: string;
}

const { title, description = "Professional real estate investment tools and calculators" } = Astro.props;
---

<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="description" content={description} />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="generator" content={Astro.generator} />

    <!-- SEO Meta Tags -->
    <title>{title}</title>
    <meta property="og:title" content={title} />
    <meta property="og:description" content={description} />
    <meta property="og:type" content="website" />
    <meta name="twitter:card" content="summary" />
    <meta name="twitter:title" content={title} />
    <meta name="twitter:description" content={description} />

    <!-- Preload critical fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />

    <!-- Performance optimizations -->
    <meta name="theme-color" content="#111827" />
    <meta name="color-scheme" content="dark" />
  </head>
  <body class="bg-gray-950 text-gray-100 font-mono">
      <main class="container mx-auto px-4 py-8 max-w-4xl">
        <slot />
      </main>

      <!-- Global styles for better UX -->
      <style is:global>
        /* Smooth scrolling */
        html {
          scroll-behavior: smooth;
        }

        /* Better focus indicators */
        *:focus-visible {
          outline: 2px solid #3B82F6;
          outline-offset: 2px;
        }

        /* Improved button interactions */
        button {
          touch-action: manipulation;
        }

        /* Better mobile tap targets */
        @media (max-width: 768px) {
          button, input, select, textarea {
            min-height: 44px;
          }
        }

        /* Reduce motion for users who prefer it */
        @media (prefers-reduced-motion: reduce) {
          *,
          *::before,
          *::after {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
          }
        }
      </style>
  </body>
</html>
