import { useCallback, useEffect, useState } from "react";

/**
 * useUrlState - Syncs a state object with the URL query parameters.
 *
 * @param initialState - The initial state object
 * @returns [state, setState]
 */
export function useUrlState<T extends Record<string, string>>(
	initialState: T,
): [T, (newState: Partial<T>) => void] {
	// Helper to parse query params into an object
	const getQueryParams = useCallback((): Partial<T> => {
		if (typeof window === "undefined") return {};
		const params = new URLSearchParams(window.location.search);
		const state: Partial<T> = {};
		for (const key in initialState) {
			const value = params.get(key);
			if (value !== null) state[key] = value as T[typeof key];
		}
		return state;
	}, [initialState]);

	// Initialize state from URL or fallback to initialState
	const [state, setStateInternal] = useState<T>(() => {
		if (typeof window === "undefined") {
			return { ...initialState } as T;
		}
		return { ...initialState, ...getQueryParams() } as T;
	});

	// Update URL when state changes
	const setState = useCallback(
		(newState: Partial<T>) => {
			setStateInternal((prev) => {
				const updated = { ...prev, ...newState };
				if (typeof window !== "undefined") {
					const params = new URLSearchParams(window.location.search);
					Object.keys(updated).forEach((key) => {
						const value = updated[key];
						if (typeof value === "string" && value !== "") {
							params.set(key, value);
						} else {
							params.delete(key);
						}
					});
					const newUrl = `${window.location.pathname}?${params.toString()}`;
					window.history.replaceState({}, "", newUrl);
				}
				return updated;
			});
		},
		[getQueryParams],
	);

	// Listen for popstate (back/forward navigation)
	useEffect(() => {
		if (typeof window === "undefined") return;
		const onPopState = () => {
			setStateInternal((prev) => ({ ...prev, ...getQueryParams() }) as T);
		};
		window.addEventListener("popstate", onPopState);
		return () => window.removeEventListener("popstate", onPopState);
	}, [getQueryParams]);

	return [state, setState];
}
