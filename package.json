{"name": "dealtools", "type": "module", "version": "0.0.1", "scripts": {"dev": "astro dev", "build": "astro build", "preview": "astro preview", "astro": "astro", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:all": "npm run test:run && npm run test:e2e"}, "dependencies": {"@astrojs/react": "^4.3.0", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-form": "^0.1.7", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/themes": "^3.2.1", "@tailwindcss/vite": "^4.1.8", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "astro": "^5.8.1", "chart.js": "^4.4.9", "chartjs-plugin-annotation": "^3.1.0", "normalize.css": "^8.0.1", "react": "^19.1.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.1.0", "tailwindcss": "^4.1.8", "zod": "^3.25.46"}, "devDependencies": {"@eslint/js": "^9.28.0", "@playwright/test": "^1.52.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@typescript-eslint/eslint-plugin": "^8.33.1", "@typescript-eslint/parser": "^8.33.1", "@vitejs/plugin-react": "^4.5.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "jsdom": "^26.1.0", "vitest": "^3.2.1"}}