# Test info

- Name: 1% Rule Calculator >> shows DEAL result for good investment (1% rule met)
- Location: /Users/<USER>/Sites/dealtools/tests/e2e/one-percent-calculator.spec.ts:55:3

# Error details

```
Error: locator.fill: Test timeout of 30000ms exceeded.
Call log:
  - waiting for getBy<PERSON><PERSON><PERSON>('Purchase Price')

    at /Users/<USER>/Sites/dealtools/tests/e2e/one-percent-calculator.spec.ts:62:45
```

# Page snapshot

```yaml
- main:
  - heading "🏠 1% RULE CALCULATOR" [level=1]
  - paragraph: "> INSTANTLY CHECK IF PROPERTY MEETS 1% RULE > ENTER PRICE + RENT FOR IMMEDIATE FEEDBACK"
  - text: "> PURCHASE_PRICE.VAL $"
  - textbox "> PURCHASE_PRICE.VAL"
  - paragraph: "> TOTAL_COST_TO_PURCHASE.DAT"
  - text: "> MONTHLY_RENT.VAL $"
  - textbox "> MONTHLY_RENT.VAL"
  - paragraph: "> EXPECTED_RENTAL_INCOME.DAT"
  - button "[EXECUTE ANALYSIS]" [disabled]
  - paragraph: "> PRESS_ENTER_OR_CLICK_TO_CALCULATE.EXE"
  - heading ">> ABOUT_THE_1_PERCENT_RULE.INFO <<" [level=2]
  - paragraph: "> THE 1% RULE IS A QUICK SCREENING TOOL USED BY REAL ESTATE INVESTORS. > IT SUGGESTS THAT A PROPERTY'S MONTHLY RENT SHOULD BE AT LEAST 1% OF > ITS PURCHASE PRICE TO POTENTIALLY BE A GOOD INVESTMENT. > WHILE NOT A GUARANTEE OF PROFITABILITY, IT HELPS FILTER OUT > PROPERTIES THAT ARE UNLIKELY TO GENERATE POSITIVE CASH FLOW."
```

# Test source

```ts
   1 | import { test, expect } from "@playwright/test";
   2 |
   3 | test.describe("1% Rule Calculator", () => {
   4 |   test.beforeEach(async ({ page }) => {
   5 |     await page.goto("/dealtools/one-percent-rule");
   6 |   });
   7 |
   8 |   test("loads the calculator page with correct elements", async ({ page }) => {
   9 |     // Check page title and heading
   10 |     await expect(page).toHaveTitle(/1% Rule Calculator/);
   11 |     await expect(
   12 |       page.getByRole("heading", { name: "🏠 1% Rule Calculator" }),
   13 |     ).toBeVisible();
   14 |
   15 |     // Check form elements
   16 |     await expect(page.getByLabel("Purchase Price")).toBeVisible();
   17 |     await expect(page.getByLabel("Expected Monthly Rent")).toBeVisible();
   18 |     await expect(
   19 |       page.getByRole("button", { name: /analyze deal/i }),
   20 |     ).toBeVisible();
   21 |
   22 |     // Check info section
   23 |     await expect(page.getByText("💡 About the 1% Rule")).toBeVisible();
   24 |   });
   25 |
   26 |   test("submit button is disabled when form is empty", async ({ page }) => {
   27 |     const submitButton = page.getByRole("button", { name: /analyze deal/i });
   28 |     await expect(submitButton).toBeDisabled();
   29 |   });
   30 |
   31 |   test("submit button is enabled when both fields are filled", async ({
   32 |     page,
   33 |   }) => {
   34 |     // Wait for the React component to hydrate
   35 |     await page.waitForLoadState("networkidle");
   36 |
   37 |     const purchasePriceInput = page.getByLabel("Purchase Price");
   38 |     const monthlyRentInput = page.getByLabel("Expected Monthly Rent");
   39 |     const submitButton = page.getByRole("button", { name: /analyze deal/i });
   40 |
   41 |     // Verify button is initially disabled
   42 |     await expect(submitButton).toBeDisabled();
   43 |
   44 |     // Fill the form
   45 |     await purchasePriceInput.fill("100000");
   46 |     await monthlyRentInput.fill("1000");
   47 |
   48 |     // Wait a moment for React state to update
   49 |     await page.waitForTimeout(100);
   50 |
   51 |     // Now button should be enabled
   52 |     await expect(submitButton).toBeEnabled();
   53 |   });
   54 |
   55 |   test("shows DEAL result for good investment (1% rule met)", async ({
   56 |     page,
   57 |   }) => {
   58 |     // Wait for the React component to hydrate
   59 |     await page.waitForLoadState("networkidle");
   60 |
   61 |     // Fill in values that meet the 1% rule
>  62 |     await page.getByLabel("Purchase Price").fill("100000");
      |                                             ^ Error: locator.fill: Test timeout of 30000ms exceeded.
   63 |     await page.getByLabel("Expected Monthly Rent").fill("1000");
   64 |
   65 |     // Wait for React state to update
   66 |     await page.waitForTimeout(100);
   67 |
   68 |     // Submit the form
   69 |     await page.getByRole("button", { name: /analyze deal/i }).click();
   70 |
   71 |     // Wait for and check the result
   72 |     await expect(page.getByText("DEAL!")).toBeVisible();
   73 |     await expect(
   74 |       page.getByText("This property meets the 1% rule!"),
   75 |     ).toBeVisible();
   76 |     await expect(page.getByText("Rent-to-Price Ratio")).toBeVisible();
   77 |     await expect(page.locator(".bg-blue-50").getByText("1.00%")).toBeVisible();
   78 |
   79 |     // Check that reset button appears
   80 |     await expect(
   81 |       page.getByRole("button", { name: /analyze another deal/i }),
   82 |     ).toBeVisible();
   83 |   });
   84 |
   85 |   test("shows NO DEAL result for poor investment (1% rule not met)", async ({
   86 |     page,
   87 |   }) => {
   88 |     // Wait for the React component to hydrate
   89 |     await page.waitForLoadState("networkidle");
   90 |
   91 |     // Fill in values that don't meet the 1% rule
   92 |     await page.getByLabel("Purchase Price").fill("100000");
   93 |     await page.getByLabel("Expected Monthly Rent").fill("800");
   94 |
   95 |     // Wait for React state to update
   96 |     await page.waitForTimeout(100);
   97 |
   98 |     // Submit the form
   99 |     await page.getByRole("button", { name: /analyze deal/i }).click();
  100 |
  101 |     // Wait for and check the result
  102 |     await expect(page.getByText("NO DEAL")).toBeVisible();
  103 |     await expect(
  104 |       page.getByText("This property doesn't meet the 1% rule."),
  105 |     ).toBeVisible();
  106 |     await expect(page.getByText("Rent-to-Price Ratio")).toBeVisible();
  107 |     await expect(page.locator(".bg-blue-50").getByText("0.80%")).toBeVisible();
  108 |   });
  109 |
  110 |   test("shows error for invalid input (zero purchase price)", async ({
  111 |     page,
  112 |   }) => {
  113 |     await page.getByLabel("Purchase Price").fill("0");
  114 |     await page.getByLabel("Expected Monthly Rent").fill("1000");
  115 |
  116 |     await page.getByRole("button", { name: /analyze deal/i }).click();
  117 |
  118 |     await expect(
  119 |       page.getByText("Purchase price must be greater than $0"),
  120 |     ).toBeVisible();
  121 |   });
  122 |
  123 |   test("shows error for invalid input (zero monthly rent)", async ({
  124 |     page,
  125 |   }) => {
  126 |     await page.getByLabel("Purchase Price").fill("100000");
  127 |     await page.getByLabel("Expected Monthly Rent").fill("0");
  128 |
  129 |     await page.getByRole("button", { name: /analyze deal/i }).click();
  130 |
  131 |     await expect(
  132 |       page.getByText("Monthly rent must be greater than $0"),
  133 |     ).toBeVisible();
  134 |   });
  135 |
  136 |   test("reset functionality works correctly", async ({ page }) => {
  137 |     // Fill and submit form
  138 |     await page.getByLabel("Purchase Price").fill("100000");
  139 |     await page.getByLabel("Expected Monthly Rent").fill("1000");
  140 |     await page.getByRole("button", { name: /analyze deal/i }).click();
  141 |
  142 |     // Wait for result
  143 |     await expect(page.getByText("DEAL!")).toBeVisible();
  144 |
  145 |     // Click reset button
  146 |     await page.getByRole("button", { name: /analyze another deal/i }).click();
  147 |
  148 |     // Check that form is reset
  149 |     await expect(page.getByLabel("Purchase Price")).toHaveValue("");
  150 |     await expect(page.getByLabel("Expected Monthly Rent")).toHaveValue("");
  151 |     await expect(page.getByText("DEAL!")).not.toBeVisible();
  152 |   });
  153 |
  154 |   test("form submission works with Enter key", async ({ page }) => {
  155 |     await page.getByLabel("Purchase Price").fill("100000");
  156 |     await page.getByLabel("Expected Monthly Rent").fill("1000");
  157 |
  158 |     // Press Enter to submit
  159 |     await page.getByLabel("Expected Monthly Rent").press("Enter");
  160 |
  161 |     await expect(page.getByText("DEAL!")).toBeVisible();
  162 |   });
```