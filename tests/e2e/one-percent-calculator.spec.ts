import { test, expect } from "@playwright/test";

test.describe("1% Rule Calculator", () => {
	test.beforeEach(async ({ page }) => {
		await page.goto("/dealtools/one-percent-rule");
	});

	test("loads the calculator page with correct elements", async ({ page }) => {
		// Check page title and heading
		await expect(page).toHaveTitle(/1% Rule Calculator/);
		await expect(
			page.getByRole("heading", { name: "🏠 1% Rule Calculator" }),
		).toBeVisible();

		// Check form elements
		await expect(page.getByLabel("Purchase Price")).toBeVisible();
		await expect(page.getByLabel("Expected Monthly Rent")).toBeVisible();
		await expect(
			page.getByRole("button", { name: /analyze deal/i }),
		).toBeVisible();

		// Check info section
		await expect(page.getByText("💡 About the 1% Rule")).toBeVisible();
	});

	test("submit button is disabled when form is empty", async ({ page }) => {
		const submitButton = page.getByRole("button", { name: /analyze deal/i });
		await expect(submitButton).toBeDisabled();
	});

	test("submit button is enabled when both fields are filled", async ({
		page,
	}) => {
		// Wait for the React component to hydrate
		await page.waitForLoadState("networkidle");

		const purchasePriceInput = page.getByLabel("Purchase Price");
		const monthlyRentInput = page.getByLabel("Expected Monthly Rent");
		const submitButton = page.getByRole("button", { name: /analyze deal/i });

		// Verify button is initially disabled
		await expect(submitButton).toBeDisabled();

		// Fill the form
		await purchasePriceInput.fill("100000");
		await monthlyRentInput.fill("1000");

		// Wait a moment for React state to update
		await page.waitForTimeout(100);

		// Now button should be enabled
		await expect(submitButton).toBeEnabled();
	});

	test("shows DEAL result for good investment (1% rule met)", async ({
		page,
	}) => {
		// Wait for the React component to hydrate
		await page.waitForLoadState("networkidle");

		// Fill in values that meet the 1% rule
		await page.getByLabel("> PURCHASE_PRICE.VAL").fill("100000");
		await page.getByLabel("> MONTHLY_RENT.VAL").fill("1000");

		// Wait for React state to update
		await page.waitForTimeout(100);

		// Submit the form
		await page.getByRole("button", { name: /analyze deal/i }).click();

		// Wait for and check the result
		await expect(page.getByText("DEAL!")).toBeVisible();
		await expect(
			page.getByText("This property meets the 1% rule!"),
		).toBeVisible();
		await expect(page.getByText("Rent-to-Price Ratio")).toBeVisible();
		await expect(page.locator(".bg-blue-50").getByText("1.00%")).toBeVisible();

		// Check that reset button appears
		await expect(
			page.getByRole("button", { name: /analyze another deal/i }),
		).toBeVisible();
	});

	test("shows NO DEAL result for poor investment (1% rule not met)", async ({
		page,
	}) => {
		// Wait for the React component to hydrate
		await page.waitForLoadState("networkidle");

		// Fill in values that don't meet the 1% rule
		await page.getByLabel("> PURCHASE_PRICE.VAL").fill("100000");
		await page.getByLabel("> MONTHLY_RENT.VAL").fill("800");

		// Wait for React state to update
		await page.waitForTimeout(100);

		// Submit the form
		await page.getByRole("button", { name: /analyze deal/i }).click();

		// Wait for and check the result
		await expect(page.getByText("NO DEAL")).toBeVisible();
		await expect(
			page.getByText("This property doesn't meet the 1% rule."),
		).toBeVisible();
		await expect(page.getByText("Rent-to-Price Ratio")).toBeVisible();
		await expect(page.locator(".bg-blue-50").getByText("0.80%")).toBeVisible();
	});

	test("shows error for invalid input (zero purchase price)", async ({
		page,
	}) => {
		await page.getByLabel("> PURCHASE_PRICE.VAL").fill("0");
		await page.getByLabel("> MONTHLY_RENT.VAL").fill("1000");

		await page.getByRole("button", { name: /execute analysis/i }).click();

		await expect(
			page.getByText("Purchase price must be greater than $0"),
		).toBeVisible();
	});

	test("shows error for invalid input (zero monthly rent)", async ({
		page,
	}) => {
		await page.getByLabel("> PURCHASE_PRICE.VAL").fill("100000");
		await page.getByLabel("> MONTHLY_RENT.VAL").fill("0");

		await page.getByRole("button", { name: /execute analysis/i }).click();

		await expect(
			page.getByText("Monthly rent must be greater than $0"),
		).toBeVisible();
	});

	test("reset functionality works correctly", async ({ page }) => {
		// Fill and submit form
		await page.getByLabel("> PURCHASE_PRICE.VAL").fill("100000");
		await page.getByLabel("> MONTHLY_RENT.VAL").fill("1000");
		await page.getByRole("button", { name: /execute analysis/i }).click();

		// Wait for result
		await expect(page.getByText("DEAL!")).toBeVisible();

		// Click reset button
		await page
			.getByRole("button", { name: /reset.*analyze.*another.*deal/i })
			.click();

		// Check that form is reset
		await expect(page.getByLabel("> PURCHASE_PRICE.VAL")).toHaveValue("");
		await expect(page.getByLabel("> MONTHLY_RENT.VAL")).toHaveValue("");
		await expect(page.getByText("DEAL!")).not.toBeVisible();
	});

	test("form submission works with Enter key", async ({ page }) => {
		await page.getByLabel("> PURCHASE_PRICE.VAL").fill("100000");
		await page.getByLabel("> MONTHLY_RENT.VAL").fill("1000");

		// Press Enter to submit
		await page.getByLabel("> MONTHLY_RENT.VAL").press("Enter");

		await expect(page.getByText("DEAL!")).toBeVisible();
	});

	test("input sanitization works correctly", async ({ page }) => {
		const purchasePriceInput = page.getByLabel("> PURCHASE_PRICE.VAL");

		// Type invalid characters
		await purchasePriceInput.fill("abc123def456");

		// Should only contain numbers
		await expect(purchasePriceInput).toHaveValue("123456");
	});

	test("handles currency formatting in inputs", async ({ page }) => {
		const purchasePriceInput = page.getByLabel("> PURCHASE_PRICE.VAL");

		// Type formatted currency
		await purchasePriceInput.fill("$100,000");

		// Should accept the formatting
		await expect(purchasePriceInput).toHaveValue("$100,000");
	});

	test("shows loading state during calculation", async ({ page }) => {
		await page.getByLabel("> PURCHASE_PRICE.VAL").fill("100000");
		await page.getByLabel("> MONTHLY_RENT.VAL").fill("1000");

		// Click submit and immediately check for loading state
		await page.getByRole("button", { name: /execute analysis/i }).click();

		// Should show loading text briefly
		await expect(page.getByText("ANALYZING...")).toBeVisible();

		// Then show result
		await expect(page.getByText("DEAL!")).toBeVisible();
	});

	test("result clears when input changes", async ({ page }) => {
		// Fill and submit form
		await page.getByLabel("> PURCHASE_PRICE.VAL").fill("100000");
		await page.getByLabel("> MONTHLY_RENT.VAL").fill("1000");
		await page.getByRole("button", { name: /execute analysis/i }).click();

		// Wait for result
		await expect(page.getByText("DEAL!")).toBeVisible();

		// Change input
		await page.getByLabel("> PURCHASE_PRICE.VAL").fill("200000");

		// Result should disappear
		await expect(page.getByText("DEAL!")).not.toBeVisible();
	});

	test("accessibility: keyboard navigation works", async ({ page }) => {
		// Tab through form elements
		await page.keyboard.press("Tab");
		await expect(page.getByLabel("Purchase Price")).toBeFocused();

		await page.keyboard.press("Tab");
		await expect(page.getByLabel("Expected Monthly Rent")).toBeFocused();

		await page.keyboard.press("Tab");
		await expect(
			page.getByRole("button", { name: /analyze deal/i }),
		).toBeFocused();
	});

	test("mobile responsiveness: form works on small screens", async ({
		page,
	}) => {
		// Set mobile viewport
		await page.setViewportSize({ width: 375, height: 667 });

		// Form should still be functional
		await page.getByLabel("Purchase Price").fill("100000");
		await page.getByLabel("Expected Monthly Rent").fill("1000");
		await page.getByRole("button", { name: /analyze deal/i }).click();

		await expect(page.getByText("DEAL!")).toBeVisible();
	});

	test("handles edge case: exactly 1% rule", async ({ page }) => {
		await page.getByLabel("Purchase Price").fill("250000");
		await page.getByLabel("Expected Monthly Rent").fill("2500");

		await page.getByRole("button", { name: /analyze deal/i }).click();

		await expect(page.getByText("DEAL!")).toBeVisible();
		await expect(page.getByText("1.00%")).toBeVisible();
	});

	test("handles high-value properties correctly", async ({ page }) => {
		await page.getByLabel("Purchase Price").fill("1000000");
		await page.getByLabel("Expected Monthly Rent").fill("8000");

		await page.getByRole("button", { name: /analyze deal/i }).click();

		await expect(page.getByText("NO DEAL")).toBeVisible();
		await expect(page.getByText("0.80%")).toBeVisible();
	});

	test("navigation from home page works", async ({ page }) => {
		// Start from home page
		await page.goto("/dealtools/");

		// Click on 1% Rule Calculator link
		await page.getByRole("link", { name: /try calculator/i }).click();

		// Should navigate to calculator page
		await expect(page).toHaveURL("/dealtools/one-percent-rule");
		await expect(
			page.getByRole("heading", { name: "🏠 1% Rule Calculator" }),
		).toBeVisible();
	});
});
