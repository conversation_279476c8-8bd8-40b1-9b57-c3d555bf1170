# Astro Starter Kit: Minimal

```sh
npm create astro@latest -- --template minimal
```

[![Open in StackBlitz](https://developer.stackblitz.com/img/open_in_stackblitz.svg)](https://stackblitz.com/github/withastro/astro/tree/latest/examples/minimal)
[![Open with CodeSandbox](https://assets.codesandbox.io/github/button-edit-lime.svg)](https://codesandbox.io/p/sandbox/github/withastro/astro/tree/latest/examples/minimal)
[![Open in GitHub Codespaces](https://github.com/codespaces/badge.svg)](https://codespaces.new/withastro/astro?devcontainer_path=.devcontainer/minimal/devcontainer.json)

> 🧑‍🚀 **Seasoned astronaut?** Delete this file. Have fun!

## 🚀 Project Structure

Inside of your Astro project, you'll see the following folders and files:

```text
/
├── public/
├── src/
│   └── pages/
│       └── index.astro
└── package.json
```

As<PERSON> looks for `.astro` or `.md` files in the `src/pages/` directory. Each page is exposed as a route based on its file name.

There's nothing special about `src/components/`, but that's where we like to put any Astro/React/Vue/Svelte/Preact components.

Any static assets, like images, can be placed in the `public/` directory.

## 🧞 Commands

All commands are run from the root of the project, from a terminal:

| Command                   | Action                                           |
| :------------------------ | :----------------------------------------------- |
| `npm install`             | Installs dependencies                            |
| `npm run dev`             | Starts local dev server at `localhost:4321`      |
| `npm run build`           | Build your production site to `./dist/`          |
| `npm run preview`         | Preview your build locally, before deploying     |
| `npm run astro ...`       | Run CLI commands like `astro add`, `astro check` |
| `npm run astro -- --help` | Get help using the Astro CLI                     |

## 🧪 Testing

This project includes comprehensive testing with both unit tests and end-to-end tests:

### Unit Tests (Vitest)

- **Framework**: Vitest with React Testing Library
- **Coverage**: Components and utility functions
- **Location**: Tests are co-located with components (e.g., `Component.test.tsx`)

### Integration Tests (Playwright)

- **Framework**: Playwright for cross-browser testing
- **Coverage**: Major user workflows and functionality
- **Location**: `tests/e2e/`

### Test Commands

| Command                   | Action                              |
| :------------------------ | :---------------------------------- |
| `npm run test`            | Run unit tests in watch mode        |
| `npm run test:run`        | Run unit tests once                 |
| `npm run test:ui`         | Run unit tests with UI              |
| `npm run test:coverage`   | Run unit tests with coverage report |
| `npm run test:e2e`        | Run end-to-end tests                |
| `npm run test:e2e:ui`     | Run e2e tests with Playwright UI    |
| `npm run test:e2e:headed` | Run e2e tests in headed mode        |
| `npm run test:all`        | Run both unit and e2e tests         |

## 🔧 Code Quality Tools

This project includes ESLint and Prettier for maintaining code quality and consistency:

- **ESLint**: Configured with TypeScript and React rules for catching errors and enforcing best practices
- **Prettier**: Automatically formats code for consistent styling
- **VS Code Integration**: Auto-format on save and lint error highlighting (see `.vscode/settings.json`)

### Configuration Files

- `eslint.config.js` - ESLint configuration using the new flat config format
- `.prettierrc` - Prettier formatting rules
- `.prettierignore` - Files to exclude from formatting

## 👀 Want to learn more?

Feel free to check [our documentation](https://docs.astro.build) or jump into our [Discord server](https://astro.build/chat).
