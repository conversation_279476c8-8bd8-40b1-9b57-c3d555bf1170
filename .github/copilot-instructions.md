## Operator Interaction

- When asked to fix code, first explain the problems found.
- When asked to generate tests, first explain what tests will be created.
- When making multiple changes, provide a step-by-step overview first.

## Security

- Check the code for vulnerabilities after generating.
- Avoid hardcoding sensitive information like credentials or API keys.
- Use secure coding practices and validate all inputs.

## Environment Variables

- If a env file exists, use it for local environment variables
- Document any new environment variables in README.md
- Provide example values in .env.example

## Version Control

- Keep commits atomic and focused on single changes
- Follow conventional commit message format
- Update «gitignore for new build artifacts or dependencies

## Code Style

- Follow existing project code style and conventions
- Add type hints and docstrings for all new functions
- Include comments for complex logic
- Use functional programming principles
- Avoid nested ifs, loops, and functions
- Avoid using regex
- Prefer early returns over if/else and nested conditionals
- Prefer declarative approaches

# Change Logging

- Each time you generate code, note the changes in CHANGELOG.md
- Follow semantic versioning guidelines
- Include date and description of changes

## Testing Requirements

- Include unit tests for new functionality
- Maintain minimum 80% code coverage
- Add integration tests for API endpoints

## Accessibility

- Ensure UI components are accessible
- Use semantic HTML and ARIA roles where appropriate
- Test with screen readers and keyboard navigation
- Follow WCAG 2.1 guidelines for color contrast and text size
- Provide alt text for images and non-text content
- Ensure forms are labeled correctly and provide error messages
- Test with various devices and browsers for compatibility
- Use responsive design techniques to ensure usability on all screen sizes
- Avoid using color as the only means of conveying information
- Provide captions and transcripts for multimedia content
- Ensure that all interactive elements are keyboard accessible
- Use clear and simple language in all text content
