# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [0.4.0] - 2025-01-15

### Added

- New `DualValueInput` component - a generic, reusable component for side-by-side dollar/percentage input fields.
- **Interactive Slider Control** - Optional slider below input fields for intuitive adjustment of dual values.
- **Real-time Section Totals** - Live calculation displays for each form section showing key financial metrics as users input data.
- Configurable props for labels, calculation base values, field IDs, placeholders, and callback functions.
- Automatic synchronization between dollar and percentage values based on configurable base value.
- Slider customization options: min/max values, step size, and visual styling.

### Changed

- **`src/components/DualValueInput.tsx`**:
  - Created unified component that consolidates functionality from `DownPaymentInput` and `ExpenseOverrideInput`.
  - Added interactive slider control with customizable range, step size, and visual styling.
  - Supports both percentage-first (down payment) and type-aware (expense override) use cases.
  - Provides flexible configuration through props for different use cases.
  - Implements real-time synchronization between input fields and slider.
- **`src/components/DealAnalyzer.tsx`**:
  - Replaced both `DownPaymentInput` and `ExpenseOverrideInput` with `DualValueInput` component.
  - Added `handleDownPaymentChange` wrapper to maintain existing down payment behavior.
  - Updated both input implementations to use the unified component with appropriate configurations.
  - Enabled slider for both Down Payment and Manual Total Expense Override inputs with 0-100% range and 0.5% step increments.
  - Added `calculateSectionTotals()` function for real-time calculation of section-specific financial metrics.
  - Implemented section total displays with color-coded borders (blue for acquisition, green for income, red for expenses).
  - Added visual indicators for manual overrides and calculated vs. final values.
- **`src/styles/global.css`**:
  - Added custom slider styling for consistent appearance across browsers.
  - Implemented cyan-colored progress track and white circular thumb with border.

### Fixed

- **Label Styling Consistency**: Removed special styling for hint text `"(Enter $ or %)"` to match other input labels with hints in the application.
- **Zero Value Display**: Fixed DualValueInput to properly display "0" when calculated values are actually zero, instead of showing blank fields which were misleading.

### Removed

- **`src/components/DownPaymentInput.tsx`** - Functionality consolidated into `DualValueInput`.
- **`src/components/ExpenseOverrideInput.tsx`** - Functionality consolidated into `DualValueInput`.

### Improved

- **Code Maintainability**: Eliminated ~95% code duplication between similar components.
- **Consistency**: Both input types now use identical UI patterns and behavior.
- **Reusability**: New `DualValueInput` component can be easily used for future dual-value input needs.
- **Type Safety**: Maintained full TypeScript support with proper prop interfaces.

---

## [0.3.0] - 2025-01-15

### Added

- New `ExpenseOverrideInput` component that provides a DownPaymentInput-style interface for the Manual Total Expense Override field.
- Side-by-side input fields for entering expense override as either dollar amount or percentage of Effective Gross Income (EGI).
- Automatic synchronization between dollar and percentage values based on current EGI calculation.

### Changed

- **`src/components/ExpenseOverrideInput.tsx`**:
  - Created new component similar to `DownPaymentInput` for expense override functionality.
  - Implements real-time conversion between dollar amounts and percentages based on EGI.
  - Provides improved user experience with visual separation between input types.
- **`src/components/DealAnalyzer.tsx`**:
  - Replaced toggle button interface for Manual Total Expense Override with new `ExpenseOverrideInput` component.
  - Removed `handleOverrideTypeChange` function as type switching is now handled internally by the new component.
  - Simplified `FormField` component by removing override-specific props and logic.
  - Added `handleExpenseOverrideChange` to work with the new component's callback interface.
  - Fixed management fee display to use `managementFeeAmount` instead of `managementFee`.

### Improved

- Enhanced user experience for expense override input to match the preferred DownPaymentInput style.
- Streamlined FormField component by removing complex override-specific conditional logic.
- Better visual consistency between DownPaymentInput and ExpenseOverrideInput components.

---

## [0.2.0] - 2025-06-01

### Added

- "Manual Total Expense Override" can now be entered as either a fixed dollar amount ($) or a percentage (%) of Effective Gross Income (EGI).
- Added a segmented control (toggle buttons for $ / %) next to the "Manual Total Expense Override" field for selecting the input type.
- New calculated metrics in `ExpensesAnalysis`: `managementFeeAmount`, `calculatedTotalBeforeOverride`, `manualExpenseOverrideValueApplied`, `manualExpenseOverrideTypeUsed`, `operatingExpenseRatio`, `expensesPerUnit`.

### Changed

- **`src/lib/dealCalculations.ts`**:
  - Updated `ExpensesParams` to replace `manualExpenseOverride` with `manualExpenseOverrideValue` (number | null) and `manualExpenseOverrideType` ('dollar' | 'percent').
  - Updated `ExpensesAnalysis` interface with new fields for detailed expense breakdown and override information.
  - Modified `calculateFullDealAnalysis` to correctly calculate EGI, management fee amount, and apply the new manual expense override logic (dollar or percent of EGI).
- **`src/components/DealAnalyzer.tsx`**:
  - Updated `initialDealParams` to use the new `manualExpenseOverrideValue` and `manualExpenseOverrideType` fields.
  - Enhanced `FormField` component to include UI (toggle buttons) and logic for the dollar/percent selection for the manual expense override.
  - Updated `useEffect` hook to correctly process new override fields from URL state for calculations.
  - Refined `handleInputChange` and added `handleOverrideTypeChange` to manage state updates for the override value and type.
  - Updated JSX to render the new toggle buttons for manual expense override and display more detailed expense analysis results.
- Replaced `SpreadsheetInput.tsx` with `FormattedInputField.tsx` for all numeric inputs to improve user experience with formatted values.

### Fixed

- Corrected order of operations and declarations in `calculateFullDealAnalysis` to resolve previous TypeScript errors.

---

## [0.1.0] - 2025-06-01

### Added

- Initial project setup with Astro, React, and Tailwind CSS.
- Core components: `DealAnalyzer`, `DealForm`.
- Hook for URL state management: `useUrlState`.
- Basic calculation logic in `dealCalculations.ts`.
- Basic `index.astro` page to render the application.

### Fixed

- Resolved TypeScript errors in `DealAnalyzer.tsx` related to type mismatches when processing URL state and missing `askingPrice` in `initialDealParams`.
- Improved type handling in `DealAnalyzer.tsx`'s `useEffect` hook for robust parsing of URL parameters.

### Changed

- Refactored `DealAnalyzer.tsx` to integrate `SpreadsheetInput.tsx` and manage form state based on `DealParams`.
- Updated `SpreadsheetInput.tsx` to accept a `placeholder` prop.
- Refactored `useUrlState.ts` for more flexible type handling.
